{"model": {"base_model": "meta-llama/Llama-2-7b-chat-hf", "model_type": "llama", "load_in_4bit": false, "load_in_8bit": false, "torch_dtype": "float16"}, "training": {"output_dir": "./ai/models/pixelated_empathy", "num_train_epochs": 3, "per_device_train_batch_size": 4, "per_device_eval_batch_size": 4, "gradient_accumulation_steps": 4, "learning_rate": 0.0002, "weight_decay": 0.001, "warmup_ratio": 0.03, "lr_scheduler_type": "cosine", "logging_steps": 10, "evaluation_strategy": "steps", "eval_steps": 500, "save_steps": 1000, "save_total_limit": 3, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "report_to": "wandb", "run_name": "pixelated_empathy_training"}, "lora": {"r": 16, "lora_alpha": 32, "target_modules": ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_dropout": 0.1, "bias": "none", "task_type": "CAUSAL_LM"}, "data": {"train_file": "./ai/training_data/pixelated_empathy_train_*.jsonl", "validation_file": "./ai/training_data/pixelated_empathy_val_*.jsonl", "test_file": "./ai/training_data/pixelated_empathy_test_*.jsonl", "max_seq_length": 2048, "preprocessing_num_workers": 4}, "system": {"dataloader_num_workers": 4, "dataloader_pin_memory": true, "fp16": false, "bf16": true, "gradient_checkpointing": true, "remove_unused_columns": false}}
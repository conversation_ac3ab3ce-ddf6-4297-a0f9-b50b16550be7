# Enhanced Dataset Integrator Requirements
# Core ML and Data Processing
datasets>=2.14.0
transformers>=4.34.0
torch>=2.0.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Data Processing and Quality Assessment
polars>=0.19.0
jsonlines>=3.1.0
nltk>=3.8.1
textstat>=0.7.3

# Utilities
tqdm>=4.65.0
loguru>=0.7.0
python-dateutil>=2.8.2
pydantic>=2.3.0

# Optional: For advanced processing
spacy>=3.7.0
sentence-transformers>=2.2.2
accelerate>=0.21.0

# Development and Testing
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0 
"""
Standard conversation format schema for the dataset pipeline.
Defines the canonical structure for conversational data.
"""

from typing import List, Optional
from pydantic import BaseModel, Field
import datetime


class Message(BaseModel):
    role: str = Field(
        ..., description="Role of the speaker (e.g., 'user', 'assistant', 'therapist', 'client')"
    )
    content: str = Field(..., description="Text content of the message")
    timestamp: Optional[datetime.datetime] = Field(
        None, description="Timestamp of the message (optional)"
    )
    meta: Optional[dict] = Field(
        default_factory=lambda: {}, description="Optional metadata for the message"
    )


class Conversation(BaseModel):
    id: Optional[str] = Field(None, description="Unique identifier for the conversation")
    messages: List[Message] = Field(
        ..., description="List of messages in the conversation, in order"
    )
    context: Optional[dict] = Field(
        default_factory=lambda: {}, description="Optional context or scenario metadata"
    )
    source: Optional[str] = Field(None, description="Source dataset or file")
    created_at: Optional[datetime.datetime] = Field(
        None, description="Conversation creation time (optional)"
    )
    meta: Optional[dict] = Field(
        default_factory=lambda: {}, description="Additional metadata for the conversation"
    )

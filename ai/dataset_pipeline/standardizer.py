"""
Data format standardization and conversion for the dataset pipeline.
Implements converters to transform raw data into the standard conversation schema.
"""

from typing import Any, Dict, List
from ai.dataset_pipeline.conversation_schema import Conversation, Message
import datetime

from typing import Optional


def from_simple_message_list(
    messages: List[Dict[str, Any]],
    conversation_id: Optional[str] = None,
    source: Optional[str] = None,
) -> Conversation:
    """
    Convert a list of dicts with 'role' and 'content' to a Conversation.
    Each dict: {'role': str, 'content': str, ...}
    """
    msg_objs = []
    for m in messages:
        msg_objs.append(
            Message(
                role=m["role"],
                content=m["content"],
                timestamp=m.get("timestamp"),
                meta=m.get("meta", {}),
            )
        )
    return Conversation(
        id=conversation_id, messages=msg_objs, source=source, created_at=datetime.datetime.now()
    )


def from_input_output_pair(
    input_text: str,
    output_text: str,
    input_role: str = "user",
    output_role: str = "assistant",
    conversation_id: Optional[str] = None,
    source: Optional[str] = None,
) -> Conversation:
    """
    Convert a simple input/output pair to a Conversation.
    """
    msgs = [
        Message(role=input_role, content=input_text, timestamp=None, meta={}),
        Message(role=output_role, content=output_text, timestamp=None, meta={}),
    ]
    return Conversation(
        id=conversation_id, messages=msgs, source=source, created_at=datetime.datetime.now()
    )

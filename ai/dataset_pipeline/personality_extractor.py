"""
Personality Marker Extraction System for Voice Training Data.

This module extracts personality markers, communication styles, and emotional
patterns from transcribed text using NLP and Big Five psychological frameworks
to enhance voice training data with personality insights.
"""

import json
import logging
import re
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union

import numpy as np

# NLP imports with fallbacks
try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.sentiment import SentimentIntensityAnalyzer
    from nltk.tokenize import word_tokenize, sent_tokenize
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False

from .logger import setup_logger


class PersonalityDimension(Enum):
    """Big Five personality dimensions."""
    OPENNESS = "openness"
    CONSCIENTIOUSNESS = "conscientiousness"
    EXTRAVERSION = "extraversion"
    AGREEABLENESS = "agreeableness"
    NEUROTICISM = "neuroticism"


class CommunicationStyle(Enum):
    """Communication style categories."""
    FORMAL = "formal"
    INFORMAL = "informal"
    ASSERTIVE = "assertive"
    PASSIVE = "passive"
    ANALYTICAL = "analytical"
    EMOTIONAL = "emotional"
    DIRECT = "direct"
    INDIRECT = "indirect"


@dataclass
class PersonalityScore:
    """Score for a personality dimension."""
    dimension: PersonalityDimension
    score: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    indicators: List[str] = field(default_factory=list)
    evidence: List[str] = field(default_factory=list)


@dataclass
class CommunicationPattern:
    """Communication style pattern."""
    style: CommunicationStyle
    strength: float  # 0.0 to 1.0
    frequency: int
    examples: List[str] = field(default_factory=list)


@dataclass
class EmotionalProfile:
    """Emotional characteristics profile."""
    dominant_emotions: List[str]
    emotional_range: float  # 0.0 to 1.0
    emotional_stability: float  # 0.0 to 1.0
    empathy_indicators: List[str]
    sentiment_distribution: Dict[str, float]
    emotional_vocabulary_richness: float


@dataclass
class PersonalityProfile:
    """Comprehensive personality profile."""
    text_source: str
    personality_scores: List[PersonalityScore]
    communication_patterns: List[CommunicationPattern]
    emotional_profile: EmotionalProfile
    linguistic_features: Dict[str, float]
    confidence_score: float
    processing_time: float
    metadata: Dict = field(default_factory=dict)


class PersonalityExtractor:
    """
    Comprehensive personality marker extraction system.
    
    Features:
    - Big Five personality trait analysis
    - Communication style detection
    - Emotional pattern recognition
    - Linguistic feature extraction
    - Confidence scoring and validation
    """
    
    def __init__(self, language: str = "en"):
        self.language = language
        self.logger = setup_logger("personality_extractor")
        
        # Initialize NLP tools
        self.nlp = None
        self.sentiment_analyzer = None
        self.stopwords = set()
        
        self._initialize_nlp_tools()
        self._load_personality_lexicons()
    
    def _initialize_nlp_tools(self):
        """Initialize available NLP tools."""
        # Initialize spaCy if available
        if SPACY_AVAILABLE:
            try:
                self.nlp = spacy.load("en_core_web_sm")
                self.logger.info("✅ spaCy model loaded")
            except OSError:
                self.logger.warning("⚠️ spaCy English model not found - some features limited")
        
        # Initialize NLTK if available
        if NLTK_AVAILABLE:
            try:
                # Download required NLTK data
                nltk.download('vader_lexicon', quiet=True)
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
                
                self.sentiment_analyzer = SentimentIntensityAnalyzer()
                self.stopwords = set(stopwords.words('english'))
                self.logger.info("✅ NLTK tools initialized")
            except Exception as e:
                self.logger.warning(f"⚠️ NLTK initialization failed: {e}")
    
    def _load_personality_lexicons(self):
        """Load personality-related word lexicons."""
        # Big Five personality indicators
        self.personality_lexicons = {
            PersonalityDimension.OPENNESS: {
                'high': ['creative', 'imaginative', 'curious', 'artistic', 'innovative', 'original', 
                        'inventive', 'unconventional', 'adventurous', 'intellectual', 'abstract',
                        'philosophical', 'complex', 'sophisticated', 'experimental'],
                'low': ['conventional', 'traditional', 'practical', 'conservative', 'routine',
                       'simple', 'straightforward', 'realistic', 'down-to-earth', 'concrete']
            },
            PersonalityDimension.CONSCIENTIOUSNESS: {
                'high': ['organized', 'disciplined', 'responsible', 'reliable', 'punctual',
                        'systematic', 'thorough', 'careful', 'planned', 'methodical', 'efficient',
                        'persistent', 'determined', 'focused', 'goal-oriented'],
                'low': ['disorganized', 'careless', 'spontaneous', 'flexible', 'casual',
                       'relaxed', 'impulsive', 'unpredictable', 'scattered']
            },
            PersonalityDimension.EXTRAVERSION: {
                'high': ['outgoing', 'social', 'talkative', 'energetic', 'assertive', 'active',
                        'enthusiastic', 'gregarious', 'sociable', 'expressive', 'bold',
                        'confident', 'dominant', 'leadership', 'charismatic'],
                'low': ['quiet', 'reserved', 'introverted', 'shy', 'withdrawn', 'solitary',
                       'reflective', 'private', 'independent', 'contemplative']
            },
            PersonalityDimension.AGREEABLENESS: {
                'high': ['kind', 'cooperative', 'trusting', 'helpful', 'compassionate',
                        'sympathetic', 'generous', 'forgiving', 'considerate', 'polite',
                        'understanding', 'supportive', 'caring', 'empathetic', 'altruistic'],
                'low': ['competitive', 'skeptical', 'critical', 'demanding', 'stubborn',
                       'argumentative', 'suspicious', 'self-centered', 'harsh']
            },
            PersonalityDimension.NEUROTICISM: {
                'high': ['anxious', 'worried', 'nervous', 'stressed', 'emotional', 'moody',
                        'unstable', 'sensitive', 'reactive', 'volatile', 'insecure',
                        'fearful', 'tense', 'irritable', 'overwhelmed'],
                'low': ['calm', 'stable', 'relaxed', 'confident', 'secure', 'resilient',
                       'composed', 'even-tempered', 'unflappable', 'steady']
            }
        }
        
        # Communication style indicators
        self.communication_lexicons = {
            CommunicationStyle.FORMAL: ['therefore', 'furthermore', 'consequently', 'nevertheless',
                                      'however', 'moreover', 'accordingly', 'subsequently'],
            CommunicationStyle.INFORMAL: ['yeah', 'okay', 'cool', 'awesome', 'totally', 'like',
                                        'you know', 'I mean', 'sort of', 'kind of'],
            CommunicationStyle.ASSERTIVE: ['I believe', 'I think', 'definitely', 'certainly',
                                         'absolutely', 'clearly', 'obviously', 'undoubtedly'],
            CommunicationStyle.PASSIVE: ['maybe', 'perhaps', 'possibly', 'I guess', 'I suppose',
                                       'might', 'could be', 'not sure', 'I think maybe'],
            CommunicationStyle.ANALYTICAL: ['analyze', 'examine', 'consider', 'evaluate',
                                          'assess', 'investigate', 'research', 'study'],
            CommunicationStyle.EMOTIONAL: ['feel', 'love', 'hate', 'excited', 'passionate',
                                         'emotional', 'heart', 'soul', 'deeply'],
            CommunicationStyle.DIRECT: ['exactly', 'precisely', 'specifically', 'clearly',
                                      'straightforward', 'simply', 'basically', 'bottom line'],
            CommunicationStyle.INDIRECT: ['somewhat', 'rather', 'quite', 'fairly', 'relatively',
                                        'tends to', 'appears to', 'seems like']
        }
        
        # Emotional vocabulary
        self.emotional_vocabulary = {
            'positive': ['happy', 'joy', 'excited', 'pleased', 'delighted', 'thrilled',
                        'grateful', 'satisfied', 'content', 'optimistic', 'hopeful'],
            'negative': ['sad', 'angry', 'frustrated', 'disappointed', 'worried', 'anxious',
                        'depressed', 'upset', 'irritated', 'annoyed', 'fearful'],
            'complex': ['bittersweet', 'conflicted', 'ambivalent', 'nostalgic', 'melancholy',
                       'contemplative', 'introspective', 'philosophical', 'profound']
        }
    
    def extract_personality_profile(self, text: str, source_info: Optional[Dict] = None) -> PersonalityProfile:
        """Extract comprehensive personality profile from text."""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Extracting personality profile from {len(text)} characters of text")
            
            # Preprocess text
            processed_text = self._preprocess_text(text)
            
            # Extract personality scores
            personality_scores = self._analyze_personality_dimensions(processed_text)
            
            # Analyze communication patterns
            communication_patterns = self._analyze_communication_styles(processed_text)
            
            # Create emotional profile
            emotional_profile = self._analyze_emotional_patterns(processed_text)
            
            # Extract linguistic features
            linguistic_features = self._extract_linguistic_features(processed_text)
            
            # Calculate overall confidence
            confidence_score = self._calculate_confidence_score(
                personality_scores, communication_patterns, emotional_profile
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            profile = PersonalityProfile(
                text_source=source_info.get('source', 'unknown') if source_info else 'unknown',
                personality_scores=personality_scores,
                communication_patterns=communication_patterns,
                emotional_profile=emotional_profile,
                linguistic_features=linguistic_features,
                confidence_score=confidence_score,
                processing_time=processing_time,
                metadata={
                    'text_length': len(text),
                    'processed_length': len(processed_text),
                    'source_info': source_info or {},
                    'extraction_timestamp': datetime.now().isoformat()
                }
            )
            
            self.logger.info(f"Personality extraction complete: confidence {confidence_score:.2f}")
            return profile
            
        except Exception as e:
            self.logger.error(f"Personality extraction failed: {e}")
            return PersonalityProfile(
                text_source='error',
                personality_scores=[],
                communication_patterns=[],
                emotional_profile=EmotionalProfile(
                    dominant_emotions=[],
                    emotional_range=0.0,
                    emotional_stability=0.0,
                    empathy_indicators=[],
                    sentiment_distribution={},
                    emotional_vocabulary_richness=0.0
                ),
                linguistic_features={},
                confidence_score=0.0,
                processing_time=0.0,
                metadata={'error': str(e)}
            )
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for analysis."""
        # Basic cleaning
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        text = re.sub(r'[^\w\s\.\!\?\,\;\:]', '', text)  # Remove special chars except punctuation
        text = text.strip().lower()
        
        return text
    
    def _analyze_personality_dimensions(self, text: str) -> List[PersonalityScore]:
        """Analyze Big Five personality dimensions."""
        scores = []
        
        for dimension, lexicon in self.personality_lexicons.items():
            high_indicators = []
            low_indicators = []
            
            # Count indicators
            for word in lexicon['high']:
                if word in text:
                    high_indicators.append(word)
            
            for word in lexicon['low']:
                if word in text:
                    low_indicators.append(word)
            
            # Calculate score (0.0 = low trait, 1.0 = high trait)
            high_count = len(high_indicators)
            low_count = len(low_indicators)
            total_count = high_count + low_count
            
            if total_count > 0:
                score = high_count / total_count
                confidence = min(1.0, total_count / 5.0)  # More indicators = higher confidence
                
                all_indicators = high_indicators + [f"low_{word}" for word in low_indicators]
                evidence = [f"Found '{word}' indicating {dimension.value}" for word in high_indicators[:3]]
                
                scores.append(PersonalityScore(
                    dimension=dimension,
                    score=score,
                    confidence=confidence,
                    indicators=all_indicators,
                    evidence=evidence
                ))
            else:
                # No indicators found - neutral score with low confidence
                scores.append(PersonalityScore(
                    dimension=dimension,
                    score=0.5,
                    confidence=0.0,
                    indicators=[],
                    evidence=["No clear indicators found"]
                ))
        
        return scores

    def _analyze_communication_styles(self, text: str) -> List[CommunicationPattern]:
        """Analyze communication style patterns."""
        patterns = []

        for style, indicators in self.communication_lexicons.items():
            matches = []
            frequency = 0

            for indicator in indicators:
                if indicator in text:
                    matches.append(indicator)
                    # Count occurrences
                    frequency += text.count(indicator)

            if matches:
                # Calculate strength based on frequency and variety
                strength = min(1.0, (len(matches) / len(indicators)) * 0.7 + (frequency / 10) * 0.3)

                patterns.append(CommunicationPattern(
                    style=style,
                    strength=strength,
                    frequency=frequency,
                    examples=matches[:5]  # Limit examples
                ))

        # Sort by strength
        patterns.sort(key=lambda p: p.strength, reverse=True)
        return patterns

    def _analyze_emotional_patterns(self, text: str) -> EmotionalProfile:
        """Analyze emotional patterns and characteristics."""
        # Sentiment analysis
        sentiment_scores = {'positive': 0.0, 'negative': 0.0, 'neutral': 0.0, 'compound': 0.0}

        if self.sentiment_analyzer and NLTK_AVAILABLE:
            try:
                scores = self.sentiment_analyzer.polarity_scores(text)
                sentiment_scores = {
                    'positive': scores['pos'],
                    'negative': scores['neg'],
                    'neutral': scores['neu'],
                    'compound': scores['compound']
                }
            except Exception as e:
                self.logger.warning(f"Sentiment analysis failed: {e}")

        # Emotional vocabulary analysis
        emotional_words = {'positive': [], 'negative': [], 'complex': []}

        for category, words in self.emotional_vocabulary.items():
            for word in words:
                if word in text:
                    emotional_words[category].append(word)

        # Calculate emotional metrics
        total_emotional_words = sum(len(words) for words in emotional_words.values())
        emotional_vocabulary_richness = min(1.0, total_emotional_words / 20.0)

        # Emotional range (variety of emotions expressed)
        emotional_range = min(1.0, len(set(
            emotional_words['positive'] +
            emotional_words['negative'] +
            emotional_words['complex']
        )) / 15.0)

        # Emotional stability (balance between positive and negative)
        if sentiment_scores['positive'] + sentiment_scores['negative'] > 0:
            emotional_stability = 1.0 - abs(sentiment_scores['positive'] - sentiment_scores['negative'])
        else:
            emotional_stability = 0.5

        # Dominant emotions
        dominant_emotions = []
        if sentiment_scores['positive'] > 0.3:
            dominant_emotions.append('positive')
        if sentiment_scores['negative'] > 0.3:
            dominant_emotions.append('negative')
        if len(emotional_words['complex']) > 2:
            dominant_emotions.append('complex')

        # Empathy indicators
        empathy_indicators = []
        empathy_words = ['understand', 'feel', 'empathy', 'compassion', 'sympathy',
                        'care', 'concern', 'support', 'help', 'listen']

        for word in empathy_words:
            if word in text:
                empathy_indicators.append(word)

        return EmotionalProfile(
            dominant_emotions=dominant_emotions,
            emotional_range=emotional_range,
            emotional_stability=emotional_stability,
            empathy_indicators=empathy_indicators,
            sentiment_distribution=sentiment_scores,
            emotional_vocabulary_richness=emotional_vocabulary_richness
        )

    def _extract_linguistic_features(self, text: str) -> Dict[str, float]:
        """Extract linguistic features and patterns."""
        features = {}

        # Basic text statistics
        words = text.split()
        sentences = text.split('.')

        features['word_count'] = len(words)
        features['sentence_count'] = len(sentences)
        features['avg_words_per_sentence'] = len(words) / max(1, len(sentences))

        # Vocabulary complexity
        unique_words = set(words)
        features['vocabulary_diversity'] = len(unique_words) / max(1, len(words))

        # Sentence complexity indicators
        complex_words = [w for w in words if len(w) > 6]
        features['complex_word_ratio'] = len(complex_words) / max(1, len(words))

        # Punctuation usage
        question_marks = text.count('?')
        exclamation_marks = text.count('!')
        features['question_ratio'] = question_marks / max(1, len(sentences))
        features['exclamation_ratio'] = exclamation_marks / max(1, len(sentences))

        # First person usage (self-reference)
        first_person_words = ['i', 'me', 'my', 'myself', 'mine']
        first_person_count = sum(text.count(word) for word in first_person_words)
        features['first_person_ratio'] = first_person_count / max(1, len(words))

        # Certainty indicators
        certainty_words = ['definitely', 'certainly', 'absolutely', 'clearly', 'obviously']
        uncertainty_words = ['maybe', 'perhaps', 'possibly', 'might', 'could']

        certainty_count = sum(text.count(word) for word in certainty_words)
        uncertainty_count = sum(text.count(word) for word in uncertainty_words)

        features['certainty_ratio'] = certainty_count / max(1, len(words))
        features['uncertainty_ratio'] = uncertainty_count / max(1, len(words))

        # Advanced features with spaCy if available
        if self.nlp and SPACY_AVAILABLE:
            try:
                doc = self.nlp(text[:1000000])  # Limit text length for processing

                # Part-of-speech ratios
                pos_counts = {}
                for token in doc:
                    pos = token.pos_
                    pos_counts[pos] = pos_counts.get(pos, 0) + 1

                total_tokens = len(doc)
                if total_tokens > 0:
                    features['noun_ratio'] = pos_counts.get('NOUN', 0) / total_tokens
                    features['verb_ratio'] = pos_counts.get('VERB', 0) / total_tokens
                    features['adj_ratio'] = pos_counts.get('ADJ', 0) / total_tokens
                    features['adv_ratio'] = pos_counts.get('ADV', 0) / total_tokens

            except Exception as e:
                self.logger.warning(f"spaCy analysis failed: {e}")

        return features

    def _calculate_confidence_score(
        self,
        personality_scores: List[PersonalityScore],
        communication_patterns: List[CommunicationPattern],
        emotional_profile: EmotionalProfile
    ) -> float:
        """Calculate overall confidence score for the personality profile."""

        # Personality confidence (average of individual confidences)
        personality_confidence = 0.0
        if personality_scores:
            personality_confidence = np.mean([score.confidence for score in personality_scores])

        # Communication pattern confidence (based on strength and variety)
        communication_confidence = 0.0
        if communication_patterns:
            communication_confidence = min(1.0,
                np.mean([pattern.strength for pattern in communication_patterns]) *
                min(1.0, len(communication_patterns) / 4.0)
            )

        # Emotional analysis confidence (based on vocabulary richness and range)
        emotional_confidence = (
            emotional_profile.emotional_vocabulary_richness * 0.6 +
            emotional_profile.emotional_range * 0.4
        )

        # Overall confidence (weighted average)
        overall_confidence = (
            personality_confidence * 0.4 +
            communication_confidence * 0.3 +
            emotional_confidence * 0.3
        )

        return min(1.0, max(0.0, overall_confidence))

    def save_personality_profile(self, profile: PersonalityProfile, output_path: str):
        """Save personality profile to JSON file."""
        try:
            # Convert to serializable format
            profile_data = {
                'text_source': profile.text_source,
                'confidence_score': profile.confidence_score,
                'processing_time': profile.processing_time,
                'metadata': profile.metadata,
                'personality_scores': [
                    {
                        'dimension': score.dimension.value,
                        'score': score.score,
                        'confidence': score.confidence,
                        'indicators': score.indicators,
                        'evidence': score.evidence
                    }
                    for score in profile.personality_scores
                ],
                'communication_patterns': [
                    {
                        'style': pattern.style.value,
                        'strength': pattern.strength,
                        'frequency': pattern.frequency,
                        'examples': pattern.examples
                    }
                    for pattern in profile.communication_patterns
                ],
                'emotional_profile': {
                    'dominant_emotions': profile.emotional_profile.dominant_emotions,
                    'emotional_range': profile.emotional_profile.emotional_range,
                    'emotional_stability': profile.emotional_profile.emotional_stability,
                    'empathy_indicators': profile.emotional_profile.empathy_indicators,
                    'sentiment_distribution': profile.emotional_profile.sentiment_distribution,
                    'emotional_vocabulary_richness': profile.emotional_profile.emotional_vocabulary_richness
                },
                'linguistic_features': profile.linguistic_features
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Personality profile saved to {output_path}")

        except Exception as e:
            self.logger.error(f"Failed to save personality profile: {e}")


# Backward compatibility function
def extract_personality_markers(
    text: str,
    language: str = "en",
    output_path: Optional[str] = None
) -> PersonalityProfile:
    """
    Extract personality markers from text with enhanced capabilities.

    This function provides backward compatibility while offering
    the enhanced features of the new PersonalityExtractor.
    """
    extractor = PersonalityExtractor(language=language)
    profile = extractor.extract_personality_profile(text)

    if output_path:
        extractor.save_personality_profile(profile, output_path)

    return profile

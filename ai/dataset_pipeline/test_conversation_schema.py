import pytest
from ai.dataset_pipeline.conversation_schema import Message, Conversation
import datetime


def test_message_valid():
    msg = Message(role="user", content="Hello!", timestamp=datetime.datetime(2025, 7, 18, 12, 0, 0))
    assert msg.role == "user"
    assert msg.content == "Hello!"
    assert msg.timestamp is not None and msg.timestamp.year == 2025
    assert isinstance(msg.meta, dict)


def test_message_missing_required():
    # Missing 'role'
    with pytest.raises(Exception):
        Message(content="Missing role", timestamp=None)
    # Missing 'content'
    with pytest.raises(Exception):
        Message(role="user", timestamp=None)


def test_conversation_valid():
    msgs = [
        Message(role="user", content="Hi", timestamp=None),
        Message(role="assistant", content="Hello, how can I help?", timestamp=None),
    ]
    conv = Conversation(
        id="conv1",
        messages=msgs,
        context={"topic": "greeting"},
        source="testset",
        created_at=datetime.datetime(2025, 7, 18, 12, 0, 0),
        meta={"quality": "high"},
    )
    assert conv.id == "conv1"
    assert len(conv.messages) == 2
    assert conv.context is not None and conv.context["topic"] == "greeting"
    assert conv.source == "testset"
    assert conv.created_at is not None and conv.created_at.year == 2025
    assert conv.meta is not None and conv.meta["quality"] == "high"


def test_conversation_missing_messages():
    # messages is required
    with pytest.raises(Exception):
        Conversation(
            id="conv2", source="testset", created_at=datetime.datetime(2025, 7, 18, 12, 0, 0)
        )

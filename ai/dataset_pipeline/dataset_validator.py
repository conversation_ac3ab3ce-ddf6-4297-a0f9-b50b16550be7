"""
Dataset validation and integrity checking system for the dataset pipeline.
"""

import os

import pandas as pd

from .logger import get_logger

logger = get_logger("dataset_pipeline.dataset_validator")


def validate_dataset(path: str, required_columns=None, min_rows=1):
    """
    Validate a dataset for required columns, minimum row count, and file existence.

    Args:
        path (str): Path to the dataset file (CSV).
        required_columns (list, optional): List of required column names.
        min_rows (int, optional): Minimum number of rows required.

    Returns:
        dict: {
            "exists": bool,
            "valid_columns": bool,
            "enough_rows": bool,
            "errors": list
        }
    """
    errors = []
    exists = os.path.exists(path)
    valid_columns = True
    enough_rows = True

    if not exists:
        errors.append(f"File not found: {path}")
        return {"exists": False, "valid_columns": False, "enough_rows": False, "errors": errors}

    try:
        df = pd.read_csv(path, keep_default_na=False)
    except Exception as e:
        errors.append(f"Failed to read CSV: {e}")
        return {"exists": True, "valid_columns": False, "enough_rows": False, "errors": errors}

    if required_columns:
        missing = [col for col in required_columns if col not in df.columns]
        if missing:
            valid_columns = False
            errors.append(f"Missing columns: {missing}")

    if len(df) < min_rows:
        enough_rows = False
        errors.append(f"Not enough rows: {len(df)} < {min_rows}")

    if errors:
        logger.warning(f"Validation failed for {path}: {errors}")
    else:
        logger.info(f"Validation passed for {path}")

    return {
        "exists": exists,
        "valid_columns": valid_columns,
        "enough_rows": enough_rows,
        "errors": errors,
    }

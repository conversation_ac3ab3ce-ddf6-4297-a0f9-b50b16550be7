name: mertools
channels:
  - pytorch
  - defaults
  - anaconda
dependencies:
  - python=3.9
  - cudatoolkit
  - pip
  - pytorch=1.12.1
  - pytorch-mutex=1.0=cuda
  - torchaudio=0.12.1
  - torchvision=0.13.1
  
  - pip:
    - accelerate==0.16.0
    - aiohttp==3.8.4
    - aiosignal==1.3.1
    - async-timeout==4.0.2
    - attrs==22.2.0
    - bitsandbytes==0.37.0
    - cchardet==2.1.7
    - chardet==5.1.0
    - contourpy==1.0.7
    - cycler==0.11.0
    - filelock==3.9.0
    - fonttools==4.38.0
    - frozenlist==1.3.3
    - huggingface-hub==0.13.4
    - importlib-resources==5.12.0
    - kiwisolver==1.4.4
    - matplotlib==3.7.0
    - multidict==6.0.4
    - openai==0.27.0
    - packaging==23.0
    - psutil==5.9.4
    - pycocotools==2.0.6
    - pyparsing==3.0.9
    - python-dateutil==2.8.2
    - pyyaml==6.0
    - regex==2022.10.31
    - tokenizers==0.13.2
    - tqdm==4.64.1
    - transformers==4.28.0
    - timm==0.6.13
    - spacy==3.5.1
    - webdataset==0.2.48
    - scikit-learn==1.2.2
    - scipy==1.10.1
    - yarl==1.8.2
    - zipp==3.14.0
    - omegaconf==2.3.0
    - opencv-python==********
    - iopath==0.1.10
    - decord==0.6.0
    - tenacity==8.2.2
    - peft
    - pycocoevalcap
    - sentence-transformers
    - umap-learn
    - notebook
    - gradio==3.24.1
    - gradio-client==0.0.8
    - wandb
    - einops
    - SentencePiece
    - ftfy
    - thop
    - pytorchvideo==0.1.5

Current learning rate: 0.01
Epoch: [0][   0/1109]	Loss 2.4787 (2.4787)	Accuracy 14.844 (14.844)
Epoch: [0][  10/1109]	Loss 1.7734 (2.0876)	Accuracy 36.719 (26.385)
Epoch: [0][  20/1109]	Loss 1.6577 (1.9275)	Accuracy 43.359 (32.459)
Epoch: [0][  30/1109]	Loss 1.4762 (1.8265)	Accuracy 46.875 (35.938)
Epoch: [0][  40/1109]	Loss 1.5623 (1.7578)	Accuracy 41.797 (38.005)
Epoch: [0][  50/1109]	Loss 1.4860 (1.7077)	Accuracy 45.703 (39.461)
Epoch: [0][  60/1109]	Loss 1.4097 (1.6604)	Accuracy 48.047 (40.977)
Epoch: [0][  70/1109]	Loss 1.3121 (1.6270)	Accuracy 54.688 (42.182)
Epoch: [0][  80/1109]	Loss 1.2951 (1.5950)	Accuracy 52.734 (43.205)
Epoch: [0][  90/1109]	Loss 1.2727 (1.5717)	Accuracy 52.734 (43.896)
Epoch: [0][ 100/1109]	Loss 1.2484 (1.5473)	Accuracy 56.641 (44.551)
Epoch: [0][ 110/1109]	Loss 1.3822 (1.5322)	Accuracy 46.094 (44.897)
Epoch: [0][ 120/1109]	Loss 1.3273 (1.5199)	Accuracy 52.344 (45.174)
Epoch: [0][ 130/1109]	Loss 1.3592 (1.5063)	Accuracy 48.438 (45.477)
Epoch: [0][ 140/1109]	Loss 1.4165 (1.4961)	Accuracy 47.656 (45.806)
Epoch: [0][ 150/1109]	Loss 1.2928 (1.4857)	Accuracy 53.906 (46.047)
Epoch: [0][ 160/1109]	Loss 1.4900 (1.4779)	Accuracy 42.188 (46.232)
Epoch: [0][ 170/1109]	Loss 1.3083 (1.4720)	Accuracy 49.219 (46.354)
Epoch: [0][ 180/1109]	Loss 1.2296 (1.4687)	Accuracy 54.297 (46.458)
Epoch: [0][ 190/1109]	Loss 1.3420 (1.4651)	Accuracy 51.562 (46.599)
Epoch: [0][ 200/1109]	Loss 1.3770 (1.4580)	Accuracy 46.875 (46.774)
Epoch: [0][ 210/1109]	Loss 1.3132 (1.4487)	Accuracy 49.219 (47.053)
Epoch: [0][ 220/1109]	Loss 1.2751 (1.4413)	Accuracy 56.250 (47.308)
Epoch: [0][ 230/1109]	Loss 1.3497 (1.4362)	Accuracy 46.875 (47.453)
Epoch: [0][ 240/1109]	Loss 1.3661 (1.4303)	Accuracy 49.219 (47.637)
Epoch: [0][ 250/1109]	Loss 1.2026 (1.4263)	Accuracy 55.078 (47.751)
Epoch: [0][ 260/1109]	Loss 1.2925 (1.4221)	Accuracy 51.562 (47.890)
Epoch: [0][ 270/1109]	Loss 1.3203 (1.4178)	Accuracy 51.562 (48.021)
Epoch: [0][ 280/1109]	Loss 1.4401 (1.4144)	Accuracy 51.172 (48.134)
Epoch: [0][ 290/1109]	Loss 1.3192 (1.4111)	Accuracy 51.172 (48.266)
Epoch: [0][ 300/1109]	Loss 1.2275 (1.4074)	Accuracy 53.516 (48.386)
Epoch: [0][ 310/1109]	Loss 1.2393 (1.4027)	Accuracy 55.078 (48.548)
Epoch: [0][ 320/1109]	Loss 1.3207 (1.3984)	Accuracy 54.297 (48.678)
Epoch: [0][ 330/1109]	Loss 1.2742 (1.3938)	Accuracy 55.859 (48.838)
Epoch: [0][ 340/1109]	Loss 1.2662 (1.3905)	Accuracy 50.391 (48.919)
Epoch: [0][ 350/1109]	Loss 1.2489 (1.3867)	Accuracy 53.125 (49.048)
Epoch: [0][ 360/1109]	Loss 1.2530 (1.3838)	Accuracy 52.344 (49.162)
Epoch: [0][ 370/1109]	Loss 1.3137 (1.3808)	Accuracy 48.828 (49.252)
Epoch: [0][ 380/1109]	Loss 1.2554 (1.3782)	Accuracy 52.734 (49.339)
Epoch: [0][ 390/1109]	Loss 1.3274 (1.3766)	Accuracy 48.438 (49.402)
Epoch: [0][ 400/1109]	Loss 1.1833 (1.3732)	Accuracy 51.953 (49.484)
Epoch: [0][ 410/1109]	Loss 1.3348 (1.3695)	Accuracy 49.609 (49.591)
Epoch: [0][ 420/1109]	Loss 1.3227 (1.3667)	Accuracy 49.609 (49.685)
Epoch: [0][ 430/1109]	Loss 1.3510 (1.3652)	Accuracy 50.391 (49.735)
Epoch: [0][ 440/1109]	Loss 1.4307 (1.3621)	Accuracy 49.219 (49.849)
Epoch: [0][ 450/1109]	Loss 1.2461 (1.3598)	Accuracy 53.516 (49.932)
Epoch: [0][ 460/1109]	Loss 1.1476 (1.3576)	Accuracy 57.422 (50.001)
Epoch: [0][ 470/1109]	Loss 1.3223 (1.3549)	Accuracy 47.266 (50.078)
Epoch: [0][ 480/1109]	Loss 1.2603 (1.3525)	Accuracy 51.172 (50.149)
Epoch: [0][ 490/1109]	Loss 1.2883 (1.3508)	Accuracy 49.219 (50.190)
Epoch: [0][ 500/1109]	Loss 1.2197 (1.3480)	Accuracy 57.812 (50.280)
Epoch: [0][ 510/1109]	Loss 1.3372 (1.3461)	Accuracy 52.344 (50.334)
Epoch: [0][ 520/1109]	Loss 1.1816 (1.3443)	Accuracy 57.031 (50.393)
Epoch: [0][ 530/1109]	Loss 1.1244 (1.3418)	Accuracy 57.031 (50.464)
Epoch: [0][ 540/1109]	Loss 1.4482 (1.3403)	Accuracy 48.047 (50.542)
Epoch: [0][ 550/1109]	Loss 1.2322 (1.3385)	Accuracy 55.859 (50.605)
Epoch: [0][ 560/1109]	Loss 1.1879 (1.3373)	Accuracy 57.812 (50.644)
Epoch: [0][ 570/1109]	Loss 1.2477 (1.3358)	Accuracy 51.562 (50.703)
Epoch: [0][ 580/1109]	Loss 1.2344 (1.3344)	Accuracy 54.688 (50.739)
Epoch: [0][ 590/1109]	Loss 1.1479 (1.3329)	Accuracy 57.422 (50.783)
Epoch: [0][ 600/1109]	Loss 1.1996 (1.3318)	Accuracy 57.031 (50.807)
Epoch: [0][ 610/1109]	Loss 1.2244 (1.3306)	Accuracy 51.562 (50.832)
Epoch: [0][ 620/1109]	Loss 1.2507 (1.3292)	Accuracy 56.250 (50.878)
Epoch: [0][ 630/1109]	Loss 1.3759 (1.3278)	Accuracy 48.828 (50.916)
Epoch: [0][ 640/1109]	Loss 1.0758 (1.3263)	Accuracy 61.719 (50.961)
Epoch: [0][ 650/1109]	Loss 1.1526 (1.3247)	Accuracy 57.812 (51.008)
Epoch: [0][ 660/1109]	Loss 1.1780 (1.3231)	Accuracy 54.297 (51.055)
Epoch: [0][ 670/1109]	Loss 1.3210 (1.3218)	Accuracy 48.828 (51.100)
Epoch: [0][ 680/1109]	Loss 1.2827 (1.3206)	Accuracy 48.438 (51.135)
Epoch: [0][ 690/1109]	Loss 1.1878 (1.3196)	Accuracy 56.641 (51.177)
Epoch: [0][ 700/1109]	Loss 1.2244 (1.3187)	Accuracy 53.516 (51.207)
Epoch: [0][ 710/1109]	Loss 1.2866 (1.3175)	Accuracy 55.078 (51.249)
Epoch: [0][ 720/1109]	Loss 1.2929 (1.3166)	Accuracy 51.953 (51.269)
Epoch: [0][ 730/1109]	Loss 1.2791 (1.3151)	Accuracy 48.438 (51.312)
Epoch: [0][ 740/1109]	Loss 1.2480 (1.3141)	Accuracy 51.172 (51.350)
Epoch: [0][ 750/1109]	Loss 1.2507 (1.3131)	Accuracy 53.125 (51.376)
Epoch: [0][ 760/1109]	Loss 1.1756 (1.3120)	Accuracy 58.984 (51.425)
Epoch: [0][ 770/1109]	Loss 1.2502 (1.3107)	Accuracy 53.906 (51.460)
Epoch: [0][ 780/1109]	Loss 1.1997 (1.3096)	Accuracy 56.641 (51.489)
Epoch: [0][ 790/1109]	Loss 1.1964 (1.3078)	Accuracy 57.422 (51.555)
Epoch: [0][ 800/1109]	Loss 1.2104 (1.3065)	Accuracy 54.297 (51.602)
Epoch: [0][ 810/1109]	Loss 1.2961 (1.3058)	Accuracy 52.734 (51.637)
Epoch: [0][ 820/1109]	Loss 1.2562 (1.3046)	Accuracy 54.688 (51.675)
Epoch: [0][ 830/1109]	Loss 1.1591 (1.3034)	Accuracy 55.859 (51.708)
Epoch: [0][ 840/1109]	Loss 1.1712 (1.3020)	Accuracy 53.516 (51.748)
Epoch: [0][ 850/1109]	Loss 1.2552 (1.3010)	Accuracy 55.078 (51.786)
Epoch: [0][ 860/1109]	Loss 1.2946 (1.3004)	Accuracy 51.562 (51.796)
Epoch: [0][ 870/1109]	Loss 1.2908 (1.2998)	Accuracy 52.734 (51.815)
Epoch: [0][ 880/1109]	Loss 1.2267 (1.2988)	Accuracy 51.953 (51.847)
Epoch: [0][ 890/1109]	Loss 1.2037 (1.2974)	Accuracy 54.688 (51.899)
Epoch: [0][ 900/1109]	Loss 1.1571 (1.2962)	Accuracy 59.375 (51.954)
Epoch: [0][ 910/1109]	Loss 1.2490 (1.2953)	Accuracy 50.391 (51.977)
Epoch: [0][ 920/1109]	Loss 1.2678 (1.2939)	Accuracy 52.344 (52.017)
Epoch: [0][ 930/1109]	Loss 1.1353 (1.2927)	Accuracy 57.422 (52.049)
Epoch: [0][ 940/1109]	Loss 1.1604 (1.2917)	Accuracy 57.422 (52.076)
Epoch: [0][ 950/1109]	Loss 1.2340 (1.2910)	Accuracy 54.297 (52.101)
Epoch: [0][ 960/1109]	Loss 1.2652 (1.2903)	Accuracy 53.125 (52.123)
Epoch: [0][ 970/1109]	Loss 1.1526 (1.2894)	Accuracy 60.156 (52.152)
Epoch: [0][ 980/1109]	Loss 1.2234 (1.2886)	Accuracy 52.734 (52.184)
Epoch: [0][ 990/1109]	Loss 1.1596 (1.2874)	Accuracy 56.641 (52.226)
Epoch: [0][1000/1109]	Loss 1.2302 (1.2867)	Accuracy 51.172 (52.239)
Epoch: [0][1010/1109]	Loss 1.2358 (1.2863)	Accuracy 51.172 (52.246)
Epoch: [0][1020/1109]	Loss 1.1532 (1.2855)	Accuracy 55.859 (52.274)
Epoch: [0][1030/1109]	Loss 1.2055 (1.2846)	Accuracy 57.422 (52.308)
Epoch: [0][1040/1109]	Loss 1.1281 (1.2833)	Accuracy 57.031 (52.353)
Epoch: [0][1050/1109]	Loss 1.2892 (1.2824)	Accuracy 51.953 (52.388)
Epoch: [0][1060/1109]	Loss 1.2111 (1.2815)	Accuracy 51.953 (52.411)
Epoch: [0][1070/1109]	Loss 1.1902 (1.2806)	Accuracy 57.422 (52.439)
Epoch: [0][1080/1109]	Loss 1.3284 (1.2803)	Accuracy 51.562 (52.453)
Epoch: [0][1090/1109]	Loss 1.1095 (1.2795)	Accuracy 56.641 (52.473)
Epoch: [0][1100/1109]	Loss 1.1052 (1.2787)	Accuracy 60.156 (52.497)
Test: [ 0/14]	Loss 1.2992 (1.2992)	Accuracy 50.000 (50.000)
Test: [10/14]	Loss 2.3034 (1.0885)	Accuracy 23.828 (61.115)
 * Accuracy 58.417
Current best accuracy: 58.416690826416016
711.84415102005
Current learning rate: 0.01
Epoch: [1][   0/1109]	Loss 1.2602 (1.2602)	Accuracy 52.344 (52.344)
Epoch: [1][  10/1109]	Loss 1.2162 (1.1889)	Accuracy 60.156 (55.788)
Epoch: [1][  20/1109]	Loss 1.2201 (1.1932)	Accuracy 57.031 (55.636)
Epoch: [1][  30/1109]	Loss 1.1160 (1.2078)	Accuracy 58.594 (55.003)
Epoch: [1][  40/1109]	Loss 1.2064 (1.2148)	Accuracy 58.203 (54.830)
Epoch: [1][  50/1109]	Loss 1.2259 (1.2123)	Accuracy 52.344 (54.810)
Epoch: [1][  60/1109]	Loss 1.2450 (1.2079)	Accuracy 50.000 (54.899)
Epoch: [1][  70/1109]	Loss 1.2390 (1.2041)	Accuracy 55.078 (55.034)
Epoch: [1][  80/1109]	Loss 1.2109 (1.2025)	Accuracy 53.516 (55.112)
Epoch: [1][  90/1109]	Loss 1.1705 (1.1995)	Accuracy 55.859 (55.155)
Epoch: [1][ 100/1109]	Loss 1.1877 (1.1980)	Accuracy 53.906 (55.306)
Epoch: [1][ 110/1109]	Loss 1.0481 (1.1978)	Accuracy 61.719 (55.310)
Epoch: [1][ 120/1109]	Loss 1.0472 (1.1944)	Accuracy 61.719 (55.466)
Epoch: [1][ 130/1109]	Loss 1.3016 (1.1949)	Accuracy 48.047 (55.403)
Epoch: [1][ 140/1109]	Loss 1.1571 (1.1959)	Accuracy 60.547 (55.377)
Epoch: [1][ 150/1109]	Loss 1.0647 (1.1942)	Accuracy 57.031 (55.438)
Epoch: [1][ 160/1109]	Loss 1.2515 (1.1949)	Accuracy 56.250 (55.386)
Epoch: [1][ 170/1109]	Loss 1.2429 (1.1952)	Accuracy 54.688 (55.329)
Epoch: [1][ 180/1109]	Loss 1.1695 (1.1952)	Accuracy 55.078 (55.328)
Epoch: [1][ 190/1109]	Loss 1.2306 (1.1942)	Accuracy 54.297 (55.430)
Epoch: [1][ 200/1109]	Loss 1.1976 (1.1925)	Accuracy 48.438 (55.478)
Epoch: [1][ 210/1109]	Loss 1.0623 (1.1900)	Accuracy 58.984 (55.569)
Epoch: [1][ 220/1109]	Loss 1.0730 (1.1870)	Accuracy 60.938 (55.681)
Epoch: [1][ 230/1109]	Loss 1.2113 (1.1864)	Accuracy 52.734 (55.677)
Epoch: [1][ 240/1109]	Loss 1.1489 (1.1867)	Accuracy 56.641 (55.650)
Epoch: [1][ 250/1109]	Loss 1.2494 (1.1879)	Accuracy 53.906 (55.612)
Epoch: [1][ 260/1109]	Loss 1.1781 (1.1897)	Accuracy 56.641 (55.574)
Epoch: [1][ 270/1109]	Loss 1.2307 (1.1900)	Accuracy 52.344 (55.545)
Epoch: [1][ 280/1109]	Loss 1.1731 (1.1889)	Accuracy 55.859 (55.565)
Epoch: [1][ 290/1109]	Loss 1.1961 (1.1873)	Accuracy 55.078 (55.645)
Epoch: [1][ 300/1109]	Loss 1.2167 (1.1873)	Accuracy 57.031 (55.624)
Epoch: [1][ 310/1109]	Loss 1.1886 (1.1871)	Accuracy 52.734 (55.606)
Epoch: [1][ 320/1109]	Loss 1.1446 (1.1870)	Accuracy 55.078 (55.594)
Epoch: [1][ 330/1109]	Loss 1.0328 (1.1852)	Accuracy 58.203 (55.652)
Epoch: [1][ 340/1109]	Loss 1.1743 (1.1847)	Accuracy 54.297 (55.647)
Epoch: [1][ 350/1109]	Loss 1.1834 (1.1849)	Accuracy 57.422 (55.656)
Epoch: [1][ 360/1109]	Loss 1.2246 (1.1857)	Accuracy 54.297 (55.644)
Epoch: [1][ 370/1109]	Loss 1.2029 (1.1851)	Accuracy 55.859 (55.654)
Epoch: [1][ 380/1109]	Loss 1.2145 (1.1847)	Accuracy 57.031 (55.674)
Epoch: [1][ 390/1109]	Loss 1.2062 (1.1842)	Accuracy 53.516 (55.707)
Epoch: [1][ 400/1109]	Loss 1.0836 (1.1834)	Accuracy 62.109 (55.749)
Epoch: [1][ 410/1109]	Loss 1.1957 (1.1833)	Accuracy 56.250 (55.745)
Epoch: [1][ 420/1109]	Loss 1.1525 (1.1832)	Accuracy 57.031 (55.754)
Epoch: [1][ 430/1109]	Loss 1.1083 (1.1832)	Accuracy 57.422 (55.741)
Epoch: [1][ 440/1109]	Loss 1.0604 (1.1832)	Accuracy 62.500 (55.751)
Epoch: [1][ 450/1109]	Loss 1.0898 (1.1829)	Accuracy 58.984 (55.758)
Epoch: [1][ 460/1109]	Loss 1.2085 (1.1823)	Accuracy 54.688 (55.798)
Epoch: [1][ 470/1109]	Loss 1.1615 (1.1818)	Accuracy 54.688 (55.805)
Epoch: [1][ 480/1109]	Loss 1.2539 (1.1817)	Accuracy 55.078 (55.816)
Epoch: [1][ 490/1109]	Loss 1.2121 (1.1811)	Accuracy 54.297 (55.824)
Epoch: [1][ 500/1109]	Loss 1.2886 (1.1811)	Accuracy 53.516 (55.816)
Epoch: [1][ 510/1109]	Loss 1.1969 (1.1803)	Accuracy 52.734 (55.846)
Epoch: [1][ 520/1109]	Loss 1.1239 (1.1810)	Accuracy 55.469 (55.816)
Epoch: [1][ 530/1109]	Loss 1.0904 (1.1807)	Accuracy 57.031 (55.817)
Epoch: [1][ 540/1109]	Loss 1.2299 (1.1808)	Accuracy 50.000 (55.803)
Epoch: [1][ 550/1109]	Loss 1.1371 (1.1804)	Accuracy 54.688 (55.821)
Epoch: [1][ 560/1109]	Loss 1.0403 (1.1801)	Accuracy 62.500 (55.836)
Epoch: [1][ 570/1109]	Loss 1.1746 (1.1799)	Accuracy 57.422 (55.866)
Epoch: [1][ 580/1109]	Loss 1.1648 (1.1806)	Accuracy 60.156 (55.843)
Epoch: [1][ 590/1109]	Loss 0.9973 (1.1797)	Accuracy 60.938 (55.879)
Epoch: [1][ 600/1109]	Loss 1.1211 (1.1791)	Accuracy 58.203 (55.893)
Epoch: [1][ 610/1109]	Loss 1.2118 (1.1786)	Accuracy 48.047 (55.892)
Epoch: [1][ 620/1109]	Loss 1.3145 (1.1777)	Accuracy 50.391 (55.945)
Epoch: [1][ 630/1109]	Loss 1.0785 (1.1774)	Accuracy 59.375 (55.970)
Epoch: [1][ 640/1109]	Loss 1.1567 (1.1770)	Accuracy 58.594 (55.994)
Epoch: [1][ 650/1109]	Loss 1.1033 (1.1763)	Accuracy 60.547 (56.027)
Epoch: [1][ 660/1109]	Loss 1.2742 (1.1764)	Accuracy 56.250 (56.044)
Epoch: [1][ 670/1109]	Loss 1.2142 (1.1761)	Accuracy 56.250 (56.060)
Epoch: [1][ 680/1109]	Loss 1.1106 (1.1756)	Accuracy 60.547 (56.096)
Epoch: [1][ 690/1109]	Loss 1.1614 (1.1753)	Accuracy 56.641 (56.102)
Epoch: [1][ 700/1109]	Loss 1.0584 (1.1750)	Accuracy 59.766 (56.120)
Epoch: [1][ 710/1109]	Loss 1.1703 (1.1751)	Accuracy 55.078 (56.113)
Epoch: [1][ 720/1109]	Loss 1.0822 (1.1746)	Accuracy 61.719 (56.147)
Epoch: [1][ 730/1109]	Loss 1.1011 (1.1743)	Accuracy 60.547 (56.151)
Epoch: [1][ 740/1109]	Loss 1.1620 (1.1742)	Accuracy 54.297 (56.155)
Epoch: [1][ 750/1109]	Loss 1.1807 (1.1742)	Accuracy 58.984 (56.145)
Epoch: [1][ 760/1109]	Loss 1.2387 (1.1743)	Accuracy 53.125 (56.138)
Epoch: [1][ 770/1109]	Loss 1.2359 (1.1746)	Accuracy 54.688 (56.129)
Epoch: [1][ 780/1109]	Loss 1.1643 (1.1742)	Accuracy 55.469 (56.150)
Epoch: [1][ 790/1109]	Loss 1.2033 (1.1735)	Accuracy 55.078 (56.183)
Epoch: [1][ 800/1109]	Loss 1.2031 (1.1734)	Accuracy 55.859 (56.179)
Epoch: [1][ 810/1109]	Loss 1.1452 (1.1736)	Accuracy 55.859 (56.171)
Epoch: [1][ 820/1109]	Loss 1.1971 (1.1733)	Accuracy 55.469 (56.197)
Epoch: [1][ 830/1109]	Loss 1.0276 (1.1725)	Accuracy 60.938 (56.225)
Epoch: [1][ 840/1109]	Loss 1.1138 (1.1721)	Accuracy 59.766 (56.246)
Epoch: [1][ 850/1109]	Loss 1.1179 (1.1719)	Accuracy 54.688 (56.261)
Epoch: [1][ 860/1109]	Loss 1.1306 (1.1716)	Accuracy 53.125 (56.267)
Epoch: [1][ 870/1109]	Loss 1.2339 (1.1709)	Accuracy 55.469 (56.299)
Epoch: [1][ 880/1109]	Loss 1.2253 (1.1708)	Accuracy 55.469 (56.310)
Epoch: [1][ 890/1109]	Loss 1.0358 (1.1703)	Accuracy 64.453 (56.327)
Epoch: [1][ 900/1109]	Loss 1.1310 (1.1700)	Accuracy 59.766 (56.335)
Epoch: [1][ 910/1109]	Loss 1.1370 (1.1696)	Accuracy 58.984 (56.358)
Epoch: [1][ 920/1109]	Loss 1.0941 (1.1693)	Accuracy 61.328 (56.362)
Epoch: [1][ 930/1109]	Loss 1.1499 (1.1690)	Accuracy 56.641 (56.373)
Epoch: [1][ 940/1109]	Loss 1.1492 (1.1687)	Accuracy 53.125 (56.379)
Epoch: [1][ 950/1109]	Loss 1.1817 (1.1686)	Accuracy 53.906 (56.374)
Epoch: [1][ 960/1109]	Loss 1.1229 (1.1683)	Accuracy 58.203 (56.388)
Epoch: [1][ 970/1109]	Loss 1.0903 (1.1677)	Accuracy 57.812 (56.414)
Epoch: [1][ 980/1109]	Loss 1.1529 (1.1673)	Accuracy 52.344 (56.419)
Epoch: [1][ 990/1109]	Loss 1.0045 (1.1673)	Accuracy 60.938 (56.422)
Epoch: [1][1000/1109]	Loss 1.1766 (1.1672)	Accuracy 55.859 (56.429)
Epoch: [1][1010/1109]	Loss 1.1156 (1.1668)	Accuracy 55.859 (56.440)
Epoch: [1][1020/1109]	Loss 1.2070 (1.1668)	Accuracy 56.641 (56.447)
Epoch: [1][1030/1109]	Loss 1.2247 (1.1664)	Accuracy 56.641 (56.462)
Epoch: [1][1040/1109]	Loss 1.1094 (1.1660)	Accuracy 58.984 (56.478)
Epoch: [1][1050/1109]	Loss 1.2578 (1.1657)	Accuracy 53.125 (56.498)
Epoch: [1][1060/1109]	Loss 1.2510 (1.1655)	Accuracy 53.906 (56.508)
Epoch: [1][1070/1109]	Loss 1.1422 (1.1656)	Accuracy 58.203 (56.505)
Epoch: [1][1080/1109]	Loss 1.0086 (1.1655)	Accuracy 60.938 (56.515)
Epoch: [1][1090/1109]	Loss 1.1945 (1.1654)	Accuracy 53.906 (56.517)
Epoch: [1][1100/1109]	Loss 1.1546 (1.1656)	Accuracy 60.547 (56.506)
Test: [ 0/14]	Loss 1.1504 (1.1504)	Accuracy 60.547 (60.547)
Test: [10/14]	Loss 1.2288 (1.0752)	Accuracy 55.469 (61.683)
 * Accuracy 62.103
Current best accuracy: 62.103458404541016
940.6958158016205
Current learning rate: 0.01
Epoch: [2][   0/1109]	Loss 1.1724 (1.1724)	Accuracy 55.859 (55.859)
Epoch: [2][  10/1109]	Loss 1.1653 (1.1535)	Accuracy 54.297 (55.824)
Epoch: [2][  20/1109]	Loss 1.1996 (1.1542)	Accuracy 58.594 (56.231)
Epoch: [2][  30/1109]	Loss 1.1964 (1.1711)	Accuracy 53.125 (55.847)
Epoch: [2][  40/1109]	Loss 1.3071 (1.1748)	Accuracy 50.391 (55.764)
Epoch: [2][  50/1109]	Loss 1.2212 (1.1734)	Accuracy 56.641 (55.790)
Epoch: [2][  60/1109]	Loss 1.0474 (1.1721)	Accuracy 58.984 (55.930)
Epoch: [2][  70/1109]	Loss 0.9779 (1.1632)	Accuracy 63.281 (56.250)
Epoch: [2][  80/1109]	Loss 1.1388 (1.1601)	Accuracy 56.641 (56.346)
Epoch: [2][  90/1109]	Loss 1.1501 (1.1586)	Accuracy 54.688 (56.374)
Epoch: [2][ 100/1109]	Loss 1.0588 (1.1556)	Accuracy 61.719 (56.494)
Epoch: [2][ 110/1109]	Loss 1.0199 (1.1533)	Accuracy 58.984 (56.528)
Epoch: [2][ 120/1109]	Loss 1.1272 (1.1519)	Accuracy 58.203 (56.576)
Epoch: [2][ 130/1109]	Loss 1.0267 (1.1482)	Accuracy 61.328 (56.802)
Epoch: [2][ 140/1109]	Loss 1.1228 (1.1490)	Accuracy 60.156 (56.812)
Epoch: [2][ 150/1109]	Loss 0.9823 (1.1472)	Accuracy 66.797 (56.987)
Epoch: [2][ 160/1109]	Loss 1.0761 (1.1450)	Accuracy 59.766 (57.043)
Epoch: [2][ 170/1109]	Loss 1.1359 (1.1434)	Accuracy 57.422 (57.148)
Epoch: [2][ 180/1109]	Loss 1.1228 (1.1417)	Accuracy 59.375 (57.202)
Epoch: [2][ 190/1109]	Loss 1.2105 (1.1409)	Accuracy 55.078 (57.260)
Epoch: [2][ 200/1109]	Loss 1.1046 (1.1417)	Accuracy 60.938 (57.210)
Epoch: [2][ 210/1109]	Loss 1.1470 (1.1404)	Accuracy 55.078 (57.248)
Epoch: [2][ 220/1109]	Loss 1.1146 (1.1393)	Accuracy 57.031 (57.341)
Epoch: [2][ 230/1109]	Loss 1.1552 (1.1402)	Accuracy 56.250 (57.282)
Epoch: [2][ 240/1109]	Loss 1.1099 (1.1388)	Accuracy 55.859 (57.331)
Epoch: [2][ 250/1109]	Loss 1.0081 (1.1380)	Accuracy 60.938 (57.328)
Epoch: [2][ 260/1109]	Loss 1.1100 (1.1382)	Accuracy 59.375 (57.283)
Epoch: [2][ 270/1109]	Loss 1.2663 (1.1379)	Accuracy 51.953 (57.289)
Epoch: [2][ 280/1109]	Loss 1.1466 (1.1367)	Accuracy 57.422 (57.343)
Epoch: [2][ 290/1109]	Loss 1.0445 (1.1363)	Accuracy 62.109 (57.384)
Epoch: [2][ 300/1109]	Loss 1.1817 (1.1363)	Accuracy 58.203 (57.371)
Epoch: [2][ 310/1109]	Loss 1.1071 (1.1357)	Accuracy 59.766 (57.401)
Epoch: [2][ 320/1109]	Loss 1.1721 (1.1353)	Accuracy 60.547 (57.407)
Epoch: [2][ 330/1109]	Loss 1.2121 (1.1352)	Accuracy 55.469 (57.449)
Epoch: [2][ 340/1109]	Loss 1.1140 (1.1344)	Accuracy 60.156 (57.460)
Epoch: [2][ 350/1109]	Loss 1.0213 (1.1336)	Accuracy 60.547 (57.505)
Epoch: [2][ 360/1109]	Loss 1.2234 (1.1343)	Accuracy 53.906 (57.493)
Epoch: [2][ 370/1109]	Loss 1.2039 (1.1343)	Accuracy 51.562 (57.480)
Epoch: [2][ 380/1109]	Loss 1.1413 (1.1339)	Accuracy 55.859 (57.508)
Epoch: [2][ 390/1109]	Loss 1.1710 (1.1334)	Accuracy 56.250 (57.552)
Epoch: [2][ 400/1109]	Loss 1.1251 (1.1327)	Accuracy 55.078 (57.574)
Epoch: [2][ 410/1109]	Loss 1.0744 (1.1330)	Accuracy 58.594 (57.545)
Epoch: [2][ 420/1109]	Loss 1.0919 (1.1336)	Accuracy 57.031 (57.517)
Epoch: [2][ 430/1109]	Loss 1.1408 (1.1331)	Accuracy 56.250 (57.534)
Epoch: [2][ 440/1109]	Loss 1.1009 (1.1322)	Accuracy 61.719 (57.575)
Epoch: [2][ 450/1109]	Loss 1.1182 (1.1317)	Accuracy 62.109 (57.595)
Epoch: [2][ 460/1109]	Loss 1.1813 (1.1311)	Accuracy 54.297 (57.610)
Epoch: [2][ 470/1109]	Loss 1.1820 (1.1313)	Accuracy 59.375 (57.615)
Epoch: [2][ 480/1109]	Loss 1.1723 (1.1314)	Accuracy 55.859 (57.616)
Epoch: [2][ 490/1109]	Loss 1.1571 (1.1322)	Accuracy 58.203 (57.606)
Epoch: [2][ 500/1109]	Loss 1.0760 (1.1329)	Accuracy 58.984 (57.580)
Epoch: [2][ 510/1109]	Loss 1.1622 (1.1324)	Accuracy 56.641 (57.611)
Epoch: [2][ 520/1109]	Loss 1.1069 (1.1321)	Accuracy 58.203 (57.614)
Epoch: [2][ 530/1109]	Loss 1.2075 (1.1327)	Accuracy 52.734 (57.589)
Epoch: [2][ 540/1109]	Loss 1.1470 (1.1319)	Accuracy 60.156 (57.631)
Epoch: [2][ 550/1109]	Loss 1.2139 (1.1315)	Accuracy 56.250 (57.644)
Epoch: [2][ 560/1109]	Loss 1.0670 (1.1314)	Accuracy 58.203 (57.642)
Epoch: [2][ 570/1109]	Loss 1.1293 (1.1314)	Accuracy 58.984 (57.652)
Epoch: [2][ 580/1109]	Loss 1.0145 (1.1307)	Accuracy 63.281 (57.678)
Epoch: [2][ 590/1109]	Loss 1.0649 (1.1301)	Accuracy 59.375 (57.692)
Epoch: [2][ 600/1109]	Loss 1.0893 (1.1299)	Accuracy 61.719 (57.707)
Epoch: [2][ 610/1109]	Loss 1.1515 (1.1294)	Accuracy 57.812 (57.728)
Epoch: [2][ 620/1109]	Loss 1.0517 (1.1292)	Accuracy 60.547 (57.740)
Epoch: [2][ 630/1109]	Loss 1.1092 (1.1295)	Accuracy 55.078 (57.727)
Epoch: [2][ 640/1109]	Loss 1.1168 (1.1298)	Accuracy 56.641 (57.721)
Epoch: [2][ 650/1109]	Loss 1.1281 (1.1299)	Accuracy 58.594 (57.735)
Epoch: [2][ 660/1109]	Loss 1.0486 (1.1297)	Accuracy 60.156 (57.757)
Epoch: [2][ 670/1109]	Loss 1.1594 (1.1296)	Accuracy 55.469 (57.747)
Epoch: [2][ 680/1109]	Loss 1.0758 (1.1290)	Accuracy 61.719 (57.769)
Epoch: [2][ 690/1109]	Loss 1.0610 (1.1289)	Accuracy 62.109 (57.775)
Epoch: [2][ 700/1109]	Loss 1.0591 (1.1284)	Accuracy 60.156 (57.803)
Epoch: [2][ 710/1109]	Loss 1.0248 (1.1283)	Accuracy 62.891 (57.797)
Epoch: [2][ 720/1109]	Loss 1.0060 (1.1279)	Accuracy 64.453 (57.813)
Epoch: [2][ 730/1109]	Loss 1.0528 (1.1283)	Accuracy 60.547 (57.808)
Epoch: [2][ 740/1109]	Loss 1.1298 (1.1282)	Accuracy 56.250 (57.811)
Epoch: [2][ 750/1109]	Loss 1.1618 (1.1288)	Accuracy 57.031 (57.806)
Epoch: [2][ 760/1109]	Loss 1.0091 (1.1285)	Accuracy 66.016 (57.823)
Epoch: [2][ 770/1109]	Loss 1.0878 (1.1283)	Accuracy 61.328 (57.838)
Epoch: [2][ 780/1109]	Loss 1.0284 (1.1277)	Accuracy 64.844 (57.865)
Epoch: [2][ 790/1109]	Loss 1.0685 (1.1272)	Accuracy 59.375 (57.884)
Epoch: [2][ 800/1109]	Loss 1.0182 (1.1268)	Accuracy 62.500 (57.904)
Epoch: [2][ 810/1109]	Loss 1.0249 (1.1266)	Accuracy 64.844 (57.919)
Epoch: [2][ 820/1109]	Loss 1.1636 (1.1269)	Accuracy 56.641 (57.911)
Epoch: [2][ 830/1109]	Loss 1.1015 (1.1269)	Accuracy 58.203 (57.904)
Epoch: [2][ 840/1109]	Loss 1.1729 (1.1270)	Accuracy 55.859 (57.907)
Epoch: [2][ 850/1109]	Loss 1.1973 (1.1270)	Accuracy 52.344 (57.896)
Epoch: [2][ 860/1109]	Loss 1.0439 (1.1267)	Accuracy 61.719 (57.896)
Epoch: [2][ 870/1109]	Loss 1.0679 (1.1271)	Accuracy 60.156 (57.874)
Epoch: [2][ 880/1109]	Loss 1.1058 (1.1271)	Accuracy 57.031 (57.867)
Epoch: [2][ 890/1109]	Loss 1.0842 (1.1268)	Accuracy 64.062 (57.873)
Epoch: [2][ 900/1109]	Loss 1.2584 (1.1267)	Accuracy 52.734 (57.875)
Epoch: [2][ 910/1109]	Loss 1.0637 (1.1264)	Accuracy 58.203 (57.887)
Epoch: [2][ 920/1109]	Loss 1.1549 (1.1264)	Accuracy 58.203 (57.895)
Epoch: [2][ 930/1109]	Loss 0.9885 (1.1261)	Accuracy 64.453 (57.903)
Epoch: [2][ 940/1109]	Loss 1.0498 (1.1258)	Accuracy 58.203 (57.920)
Epoch: [2][ 950/1109]	Loss 1.1786 (1.1256)	Accuracy 58.594 (57.930)
Epoch: [2][ 960/1109]	Loss 1.0239 (1.1254)	Accuracy 58.203 (57.939)
Epoch: [2][ 970/1109]	Loss 1.1474 (1.1255)	Accuracy 57.812 (57.930)
Epoch: [2][ 980/1109]	Loss 1.2476 (1.1254)	Accuracy 55.078 (57.934)
Epoch: [2][ 990/1109]	Loss 1.1299 (1.1254)	Accuracy 57.422 (57.943)
Epoch: [2][1000/1109]	Loss 1.1626 (1.1251)	Accuracy 55.469 (57.952)
Epoch: [2][1010/1109]	Loss 0.9385 (1.1251)	Accuracy 67.969 (57.950)
Epoch: [2][1020/1109]	Loss 1.0122 (1.1248)	Accuracy 61.328 (57.961)
Epoch: [2][1030/1109]	Loss 1.1408 (1.1249)	Accuracy 57.031 (57.965)
Epoch: [2][1040/1109]	Loss 1.1456 (1.1249)	Accuracy 57.812 (57.983)
Epoch: [2][1050/1109]	Loss 1.2269 (1.1251)	Accuracy 53.906 (57.976)
Epoch: [2][1060/1109]	Loss 1.0173 (1.1251)	Accuracy 62.500 (57.969)
Epoch: [2][1070/1109]	Loss 1.1164 (1.1251)	Accuracy 58.203 (57.978)
Epoch: [2][1080/1109]	Loss 1.1017 (1.1248)	Accuracy 56.250 (57.996)
Epoch: [2][1090/1109]	Loss 1.0529 (1.1245)	Accuracy 62.500 (58.017)
Epoch: [2][1100/1109]	Loss 1.1082 (1.1242)	Accuracy 55.859 (58.030)
Test: [ 0/14]	Loss 1.1084 (1.1084)	Accuracy 62.109 (62.109)
Test: [10/14]	Loss 1.8518 (1.0130)	Accuracy 42.578 (63.743)
 * Accuracy 62.018
Current best accuracy: 62.103458404541016
947.4319429397583
Current learning rate: 0.01
Epoch: [3][   0/1109]	Loss 1.0918 (1.0918)	Accuracy 59.766 (59.766)
Epoch: [3][  10/1109]	Loss 1.0528 (1.0802)	Accuracy 59.766 (59.588)
Epoch: [3][  20/1109]	Loss 1.0990 (1.0792)	Accuracy 60.938 (59.747)
Epoch: [3][  30/1109]	Loss 1.1736 (1.0903)	Accuracy 58.203 (59.388)
Epoch: [3][  40/1109]	Loss 1.1134 (1.0935)	Accuracy 57.422 (59.299)
Epoch: [3][  50/1109]	Loss 1.1470 (1.0999)	Accuracy 53.906 (58.801)
Epoch: [3][  60/1109]	Loss 1.0772 (1.1055)	Accuracy 60.547 (58.683)
Epoch: [3][  70/1109]	Loss 1.0707 (1.1051)	Accuracy 56.250 (58.731)
Epoch: [3][  80/1109]	Loss 0.9947 (1.1047)	Accuracy 61.719 (58.791)
Epoch: [3][  90/1109]	Loss 1.0152 (1.1044)	Accuracy 57.031 (58.787)
Epoch: [3][ 100/1109]	Loss 1.2556 (1.1074)	Accuracy 52.734 (58.687)
Epoch: [3][ 110/1109]	Loss 1.0631 (1.1104)	Accuracy 58.594 (58.580)
Epoch: [3][ 120/1109]	Loss 1.1322 (1.1099)	Accuracy 58.203 (58.545)
Epoch: [3][ 130/1109]	Loss 1.2124 (1.1086)	Accuracy 57.031 (58.633)
Epoch: [3][ 140/1109]	Loss 1.1893 (1.1096)	Accuracy 57.422 (58.616)
Epoch: [3][ 150/1109]	Loss 1.0507 (1.1075)	Accuracy 58.203 (58.692)
Epoch: [3][ 160/1109]	Loss 1.0795 (1.1065)	Accuracy 59.375 (58.771)
Epoch: [3][ 170/1109]	Loss 1.1373 (1.1076)	Accuracy 57.031 (58.749)
Epoch: [3][ 180/1109]	Loss 1.1463 (1.1070)	Accuracy 54.688 (58.743)
Epoch: [3][ 190/1109]	Loss 1.1574 (1.1094)	Accuracy 56.250 (58.614)
Epoch: [3][ 200/1109]	Loss 1.0964 (1.1113)	Accuracy 59.375 (58.563)
Epoch: [3][ 210/1109]	Loss 1.1589 (1.1095)	Accuracy 58.594 (58.618)
Epoch: [3][ 220/1109]	Loss 1.1254 (1.1106)	Accuracy 59.375 (58.578)
Epoch: [3][ 230/1109]	Loss 1.0383 (1.1094)	Accuracy 58.594 (58.616)
Epoch: [3][ 240/1109]	Loss 1.0707 (1.1085)	Accuracy 57.422 (58.639)
Epoch: [3][ 250/1109]	Loss 1.1190 (1.1082)	Accuracy 60.156 (58.656)
Epoch: [3][ 260/1109]	Loss 1.0508 (1.1086)	Accuracy 60.938 (58.666)
Epoch: [3][ 270/1109]	Loss 1.1177 (1.1091)	Accuracy 61.328 (58.643)
Epoch: [3][ 280/1109]	Loss 1.0479 (1.1105)	Accuracy 58.984 (58.617)
Epoch: [3][ 290/1109]	Loss 1.1098 (1.1109)	Accuracy 59.375 (58.613)
Epoch: [3][ 300/1109]	Loss 1.1546 (1.1123)	Accuracy 57.422 (58.559)
Epoch: [3][ 310/1109]	Loss 1.0730 (1.1112)	Accuracy 60.156 (58.598)
Epoch: [3][ 320/1109]	Loss 1.1056 (1.1109)	Accuracy 57.812 (58.631)
Epoch: [3][ 330/1109]	Loss 1.0461 (1.1113)	Accuracy 62.109 (58.655)
Epoch: [3][ 340/1109]	Loss 1.0348 (1.1120)	Accuracy 60.938 (58.606)
Epoch: [3][ 350/1109]	Loss 1.1730 (1.1113)	Accuracy 52.734 (58.600)
Epoch: [3][ 360/1109]	Loss 1.1306 (1.1113)	Accuracy 54.688 (58.566)
Epoch: [3][ 370/1109]	Loss 1.2006 (1.1109)	Accuracy 58.203 (58.594)
Epoch: [3][ 380/1109]	Loss 1.0856 (1.1099)	Accuracy 63.281 (58.653)
Epoch: [3][ 390/1109]	Loss 1.0477 (1.1092)	Accuracy 62.891 (58.720)
Epoch: [3][ 400/1109]	Loss 1.1098 (1.1083)	Accuracy 59.375 (58.781)
Epoch: [3][ 410/1109]	Loss 1.0546 (1.1082)	Accuracy 58.984 (58.794)
Epoch: [3][ 420/1109]	Loss 1.0937 (1.1090)	Accuracy 54.297 (58.737)
Epoch: [3][ 430/1109]	Loss 1.0337 (1.1086)	Accuracy 62.891 (58.760)
Epoch: [3][ 440/1109]	Loss 1.1342 (1.1077)	Accuracy 56.641 (58.791)
Epoch: [3][ 450/1109]	Loss 1.1095 (1.1079)	Accuracy 61.328 (58.796)
Epoch: [3][ 460/1109]	Loss 1.0131 (1.1076)	Accuracy 60.938 (58.797)
Epoch: [3][ 470/1109]	Loss 1.1377 (1.1077)	Accuracy 62.500 (58.817)
Epoch: [3][ 480/1109]	Loss 1.0266 (1.1074)	Accuracy 58.203 (58.811)
Epoch: [3][ 490/1109]	Loss 1.1368 (1.1070)	Accuracy 58.203 (58.829)
Epoch: [3][ 500/1109]	Loss 1.0138 (1.1072)	Accuracy 61.719 (58.811)
Epoch: [3][ 510/1109]	Loss 1.0548 (1.1066)	Accuracy 59.766 (58.835)
Epoch: [3][ 520/1109]	Loss 1.1726 (1.1065)	Accuracy 57.031 (58.840)
Epoch: [3][ 530/1109]	Loss 1.1671 (1.1067)	Accuracy 60.156 (58.838)
Epoch: [3][ 540/1109]	Loss 1.0285 (1.1066)	Accuracy 65.625 (58.842)
Epoch: [3][ 550/1109]	Loss 1.0827 (1.1070)	Accuracy 58.203 (58.815)
Epoch: [3][ 560/1109]	Loss 1.0689 (1.1066)	Accuracy 58.594 (58.829)
Epoch: [3][ 570/1109]	Loss 1.0471 (1.1063)	Accuracy 58.203 (58.829)
Epoch: [3][ 580/1109]	Loss 1.0657 (1.1056)	Accuracy 60.938 (58.861)
Epoch: [3][ 590/1109]	Loss 1.1950 (1.1063)	Accuracy 57.422 (58.849)
Epoch: [3][ 600/1109]	Loss 1.1010 (1.1062)	Accuracy 59.766 (58.855)
Epoch: [3][ 610/1109]	Loss 1.0690 (1.1060)	Accuracy 60.547 (58.862)
Epoch: [3][ 620/1109]	Loss 1.1495 (1.1056)	Accuracy 53.906 (58.868)
Epoch: [3][ 630/1109]	Loss 1.1492 (1.1053)	Accuracy 54.688 (58.882)
Epoch: [3][ 640/1109]	Loss 1.1385 (1.1055)	Accuracy 57.422 (58.867)
Epoch: [3][ 650/1109]	Loss 1.0781 (1.1052)	Accuracy 59.375 (58.881)
Epoch: [3][ 660/1109]	Loss 1.0562 (1.1052)	Accuracy 60.938 (58.880)
Epoch: [3][ 670/1109]	Loss 1.1566 (1.1050)	Accuracy 57.812 (58.880)
Epoch: [3][ 680/1109]	Loss 1.1020 (1.1046)	Accuracy 60.547 (58.893)
Epoch: [3][ 690/1109]	Loss 1.1216 (1.1048)	Accuracy 58.984 (58.891)
Epoch: [3][ 700/1109]	Loss 1.0723 (1.1047)	Accuracy 58.203 (58.886)
Epoch: [3][ 710/1109]	Loss 1.1001 (1.1043)	Accuracy 58.203 (58.899)
Epoch: [3][ 720/1109]	Loss 1.0805 (1.1039)	Accuracy 61.328 (58.913)
Epoch: [3][ 730/1109]	Loss 1.2644 (1.1039)	Accuracy 53.906 (58.907)
Epoch: [3][ 740/1109]	Loss 1.0611 (1.1033)	Accuracy 59.375 (58.921)
Epoch: [3][ 750/1109]	Loss 1.1130 (1.1033)	Accuracy 57.422 (58.914)
Epoch: [3][ 760/1109]	Loss 1.0807 (1.1034)	Accuracy 59.375 (58.906)
Epoch: [3][ 770/1109]	Loss 1.1123 (1.1037)	Accuracy 59.375 (58.895)
Epoch: [3][ 780/1109]	Loss 1.0882 (1.1037)	Accuracy 59.375 (58.892)
Epoch: [3][ 790/1109]	Loss 1.0396 (1.1035)	Accuracy 60.156 (58.902)
Epoch: [3][ 800/1109]	Loss 1.0871 (1.1032)	Accuracy 57.422 (58.915)
Epoch: [3][ 810/1109]	Loss 1.2332 (1.1031)	Accuracy 57.422 (58.922)
Epoch: [3][ 820/1109]	Loss 1.0983 (1.1029)	Accuracy 55.859 (58.931)
Epoch: [3][ 830/1109]	Loss 1.2254 (1.1029)	Accuracy 56.641 (58.935)
Epoch: [3][ 840/1109]	Loss 1.0520 (1.1024)	Accuracy 60.547 (58.961)
Epoch: [3][ 850/1109]	Loss 1.0253 (1.1018)	Accuracy 63.281 (58.974)
Epoch: [3][ 860/1109]	Loss 1.1372 (1.1019)	Accuracy 60.938 (58.955)
Epoch: [3][ 870/1109]	Loss 1.1588 (1.1021)	Accuracy 56.641 (58.940)
Epoch: [3][ 880/1109]	Loss 1.1071 (1.1021)	Accuracy 58.594 (58.926)
Epoch: [3][ 890/1109]	Loss 1.2162 (1.1020)	Accuracy 52.734 (58.933)
Epoch: [3][ 900/1109]	Loss 1.0752 (1.1018)	Accuracy 65.234 (58.944)
Epoch: [3][ 910/1109]	Loss 1.0891 (1.1016)	Accuracy 57.422 (58.946)
Epoch: [3][ 920/1109]	Loss 1.0999 (1.1014)	Accuracy 56.641 (58.933)
Epoch: [3][ 930/1109]	Loss 1.1456 (1.1015)	Accuracy 56.641 (58.936)
Epoch: [3][ 940/1109]	Loss 1.1416 (1.1015)	Accuracy 62.500 (58.936)
Epoch: [3][ 950/1109]	Loss 1.0205 (1.1016)	Accuracy 62.500 (58.932)
Epoch: [3][ 960/1109]	Loss 1.1179 (1.1015)	Accuracy 60.547 (58.943)
Epoch: [3][ 970/1109]	Loss 1.0728 (1.1010)	Accuracy 58.984 (58.963)
Epoch: [3][ 980/1109]	Loss 1.1144 (1.1007)	Accuracy 61.719 (58.976)
Epoch: [3][ 990/1109]	Loss 1.1286 (1.1005)	Accuracy 57.031 (58.982)
Epoch: [3][1000/1109]	Loss 1.0498 (1.1006)	Accuracy 60.938 (58.978)
Epoch: [3][1010/1109]	Loss 1.0617 (1.1007)	Accuracy 61.719 (58.974)
Epoch: [3][1020/1109]	Loss 1.0841 (1.1007)	Accuracy 60.156 (58.978)
Epoch: [3][1030/1109]	Loss 1.0204 (1.1006)	Accuracy 57.031 (58.975)
Epoch: [3][1040/1109]	Loss 1.2247 (1.1007)	Accuracy 55.078 (58.966)
Epoch: [3][1050/1109]	Loss 1.0416 (1.1006)	Accuracy 62.500 (58.964)
Epoch: [3][1060/1109]	Loss 1.0205 (1.1005)	Accuracy 60.938 (58.971)
Epoch: [3][1070/1109]	Loss 1.1086 (1.1004)	Accuracy 56.641 (58.961)
Epoch: [3][1080/1109]	Loss 1.1243 (1.1002)	Accuracy 57.812 (58.965)
Epoch: [3][1090/1109]	Loss 1.1362 (1.1005)	Accuracy 56.250 (58.956)
Epoch: [3][1100/1109]	Loss 1.0948 (1.1002)	Accuracy 58.594 (58.970)
Test: [ 0/14]	Loss 0.8894 (0.8894)	Accuracy 67.188 (67.188)
Test: [10/14]	Loss 2.0339 (1.0449)	Accuracy 39.844 (62.891)
 * Accuracy 61.589
Current best accuracy: 62.103458404541016
1022.921061038971
Current learning rate: 0.01
Epoch: [4][   0/1109]	Loss 1.1042 (1.1042)	Accuracy 63.281 (63.281)
Epoch: [4][  10/1109]	Loss 0.9937 (1.0958)	Accuracy 62.891 (59.517)
Epoch: [4][  20/1109]	Loss 1.0705 (1.0784)	Accuracy 57.422 (60.175)
Epoch: [4][  30/1109]	Loss 1.0951 (1.0712)	Accuracy 58.984 (60.282)
Epoch: [4][  40/1109]	Loss 1.0457 (1.0780)	Accuracy 62.891 (59.851)
Epoch: [4][  50/1109]	Loss 1.1807 (1.0822)	Accuracy 56.250 (59.643)
Epoch: [4][  60/1109]	Loss 1.1424 (1.0817)	Accuracy 60.156 (59.612)
Epoch: [4][  70/1109]	Loss 1.0430 (1.0846)	Accuracy 62.891 (59.551)
Epoch: [4][  80/1109]	Loss 1.0752 (1.0867)	Accuracy 62.109 (59.563)
Epoch: [4][  90/1109]	Loss 1.1161 (1.0863)	Accuracy 60.547 (59.568)
Epoch: [4][ 100/1109]	Loss 1.0754 (1.0877)	Accuracy 59.766 (59.561)
Epoch: [4][ 110/1109]	Loss 1.0807 (1.0849)	Accuracy 57.031 (59.576)
Epoch: [4][ 120/1109]	Loss 1.1213 (1.0838)	Accuracy 56.250 (59.601)
Epoch: [4][ 130/1109]	Loss 1.1085 (1.0854)	Accuracy 52.734 (59.548)
Epoch: [4][ 140/1109]	Loss 1.0820 (1.0860)	Accuracy 61.719 (59.494)
Epoch: [4][ 150/1109]	Loss 1.0500 (1.0848)	Accuracy 60.938 (59.546)
Epoch: [4][ 160/1109]	Loss 0.9809 (1.0838)	Accuracy 64.453 (59.562)
Epoch: [4][ 170/1109]	Loss 1.0055 (1.0833)	Accuracy 60.938 (59.617)
Epoch: [4][ 180/1109]	Loss 1.0109 (1.0808)	Accuracy 62.891 (59.768)
Epoch: [4][ 190/1109]	Loss 1.0510 (1.0818)	Accuracy 60.547 (59.733)
Epoch: [4][ 200/1109]	Loss 1.1141 (1.0816)	Accuracy 57.812 (59.760)
Epoch: [4][ 210/1109]	Loss 1.1129 (1.0796)	Accuracy 57.031 (59.823)
Epoch: [4][ 220/1109]	Loss 1.0598 (1.0790)	Accuracy 61.328 (59.836)
Epoch: [4][ 230/1109]	Loss 1.1254 (1.0795)	Accuracy 57.031 (59.816)
Epoch: [4][ 240/1109]	Loss 1.0449 (1.0803)	Accuracy 62.109 (59.813)
Epoch: [4][ 250/1109]	Loss 1.1201 (1.0802)	Accuracy 57.031 (59.853)
Epoch: [4][ 260/1109]	Loss 1.0896 (1.0802)	Accuracy 56.250 (59.855)
Epoch: [4][ 270/1109]	Loss 1.1199 (1.0794)	Accuracy 61.328 (59.911)
Epoch: [4][ 280/1109]	Loss 1.0028 (1.0801)	Accuracy 60.547 (59.863)
Epoch: [4][ 290/1109]	Loss 0.9672 (1.0806)	Accuracy 65.625 (59.865)
Epoch: [4][ 300/1109]	Loss 1.1898 (1.0824)	Accuracy 57.422 (59.766)
Epoch: [4][ 310/1109]	Loss 1.0748 (1.0835)	Accuracy 62.891 (59.694)
Epoch: [4][ 320/1109]	Loss 1.1359 (1.0838)	Accuracy 53.125 (59.689)
Epoch: [4][ 330/1109]	Loss 1.0698 (1.0833)	Accuracy 62.109 (59.733)
Epoch: [4][ 340/1109]	Loss 1.0994 (1.0839)	Accuracy 57.812 (59.704)
Epoch: [4][ 350/1109]	Loss 1.1868 (1.0851)	Accuracy 55.078 (59.685)
Epoch: [4][ 360/1109]	Loss 1.2258 (1.0850)	Accuracy 56.250 (59.693)
Epoch: [4][ 370/1109]	Loss 1.0614 (1.0859)	Accuracy 61.719 (59.652)
Epoch: [4][ 380/1109]	Loss 1.0627 (1.0850)	Accuracy 60.547 (59.677)
Epoch: [4][ 390/1109]	Loss 1.1909 (1.0847)	Accuracy 56.250 (59.698)
Epoch: [4][ 400/1109]	Loss 1.2279 (1.0846)	Accuracy 53.516 (59.701)
Epoch: [4][ 410/1109]	Loss 0.8896 (1.0840)	Accuracy 68.750 (59.728)
Epoch: [4][ 420/1109]	Loss 1.0169 (1.0840)	Accuracy 63.672 (59.729)
Epoch: [4][ 430/1109]	Loss 1.0130 (1.0833)	Accuracy 61.719 (59.770)
Epoch: [4][ 440/1109]	Loss 1.0780 (1.0831)	Accuracy 57.031 (59.770)
Epoch: [4][ 450/1109]	Loss 1.0680 (1.0831)	Accuracy 59.766 (59.773)
Epoch: [4][ 460/1109]	Loss 1.0412 (1.0827)	Accuracy 62.109 (59.784)
Epoch: [4][ 470/1109]	Loss 1.1181 (1.0826)	Accuracy 57.812 (59.800)
Epoch: [4][ 480/1109]	Loss 1.0690 (1.0823)	Accuracy 60.547 (59.820)
Epoch: [4][ 490/1109]	Loss 1.1024 (1.0825)	Accuracy 58.594 (59.805)
Epoch: [4][ 500/1109]	Loss 1.1017 (1.0832)	Accuracy 55.078 (59.770)
Epoch: [4][ 510/1109]	Loss 1.1686 (1.0833)	Accuracy 59.375 (59.760)
Epoch: [4][ 520/1109]	Loss 1.1498 (1.0834)	Accuracy 55.469 (59.769)
Epoch: [4][ 530/1109]	Loss 1.0276 (1.0831)	Accuracy 59.375 (59.772)
Epoch: [4][ 540/1109]	Loss 1.2238 (1.0836)	Accuracy 57.031 (59.742)
Epoch: [4][ 550/1109]	Loss 1.1882 (1.0841)	Accuracy 57.031 (59.727)
Epoch: [4][ 560/1109]	Loss 1.0063 (1.0839)	Accuracy 63.281 (59.720)
Epoch: [4][ 570/1109]	Loss 1.0884 (1.0833)	Accuracy 56.641 (59.741)
Epoch: [4][ 580/1109]	Loss 1.0838 (1.0836)	Accuracy 58.984 (59.737)
Epoch: [4][ 590/1109]	Loss 0.9667 (1.0839)	Accuracy 63.672 (59.719)
Epoch: [4][ 600/1109]	Loss 1.0682 (1.0844)	Accuracy 60.547 (59.718)
Epoch: [4][ 610/1109]	Loss 1.0839 (1.0844)	Accuracy 58.594 (59.714)
Epoch: [4][ 620/1109]	Loss 1.2672 (1.0846)	Accuracy 53.125 (59.710)
Epoch: [4][ 630/1109]	Loss 1.0458 (1.0851)	Accuracy 60.156 (59.675)
Epoch: [4][ 640/1109]	Loss 1.1495 (1.0849)	Accuracy 57.422 (59.688)
Epoch: [4][ 650/1109]	Loss 1.0317 (1.0846)	Accuracy 60.156 (59.692)
Epoch: [4][ 660/1109]	Loss 1.1191 (1.0847)	Accuracy 58.594 (59.701)
Epoch: [4][ 670/1109]	Loss 1.0504 (1.0845)	Accuracy 59.375 (59.692)
Epoch: [4][ 680/1109]	Loss 1.0565 (1.0846)	Accuracy 62.500 (59.693)
Epoch: [4][ 690/1109]	Loss 1.1691 (1.0845)	Accuracy 51.953 (59.699)
Epoch: [4][ 700/1109]	Loss 1.0349 (1.0840)	Accuracy 64.062 (59.727)
Epoch: [4][ 710/1109]	Loss 1.0539 (1.0838)	Accuracy 58.203 (59.733)
Epoch: [4][ 720/1109]	Loss 1.1328 (1.0835)	Accuracy 56.250 (59.751)
Epoch: [4][ 730/1109]	Loss 1.0465 (1.0831)	Accuracy 60.547 (59.767)
Epoch: [4][ 740/1109]	Loss 0.9945 (1.0831)	Accuracy 61.328 (59.756)
Epoch: [4][ 750/1109]	Loss 1.0343 (1.0831)	Accuracy 62.109 (59.741)
Epoch: [4][ 760/1109]	Loss 1.0000 (1.0825)	Accuracy 60.156 (59.759)
Epoch: [4][ 770/1109]	Loss 1.1342 (1.0822)	Accuracy 58.203 (59.781)
Epoch: [4][ 780/1109]	Loss 1.0637 (1.0821)	Accuracy 64.062 (59.791)
Epoch: [4][ 790/1109]	Loss 1.0635 (1.0818)	Accuracy 57.422 (59.797)
Epoch: [4][ 800/1109]	Loss 1.0624 (1.0817)	Accuracy 57.812 (59.810)
Epoch: [4][ 810/1109]	Loss 1.1326 (1.0812)	Accuracy 55.469 (59.817)
Epoch: [4][ 820/1109]	Loss 1.0587 (1.0810)	Accuracy 61.328 (59.816)
Epoch: [4][ 830/1109]	Loss 1.0064 (1.0810)	Accuracy 62.500 (59.829)
Epoch: [4][ 840/1109]	Loss 1.1695 (1.0810)	Accuracy 57.031 (59.842)
Epoch: [4][ 850/1109]	Loss 1.0789 (1.0806)	Accuracy 61.719 (59.862)
Epoch: [4][ 860/1109]	Loss 1.0334 (1.0807)	Accuracy 63.281 (59.861)
Epoch: [4][ 870/1109]	Loss 0.9766 (1.0807)	Accuracy 64.453 (59.864)
Epoch: [4][ 880/1109]	Loss 0.9708 (1.0806)	Accuracy 64.453 (59.866)
Epoch: [4][ 890/1109]	Loss 1.0622 (1.0804)	Accuracy 59.375 (59.875)
Epoch: [4][ 900/1109]	Loss 1.0398 (1.0806)	Accuracy 58.984 (59.865)
Epoch: [4][ 910/1109]	Loss 1.1199 (1.0808)	Accuracy 57.031 (59.863)
Epoch: [4][ 920/1109]	Loss 1.0885 (1.0805)	Accuracy 59.766 (59.879)
Epoch: [4][ 930/1109]	Loss 1.0096 (1.0804)	Accuracy 62.109 (59.881)
Epoch: [4][ 940/1109]	Loss 1.0023 (1.0803)	Accuracy 60.938 (59.893)
Epoch: [4][ 950/1109]	Loss 1.0771 (1.0803)	Accuracy 59.375 (59.889)
Epoch: [4][ 960/1109]	Loss 1.0335 (1.0800)	Accuracy 59.766 (59.894)
Epoch: [4][ 970/1109]	Loss 1.0458 (1.0798)	Accuracy 60.156 (59.892)
Epoch: [4][ 980/1109]	Loss 1.1201 (1.0801)	Accuracy 55.469 (59.874)
Epoch: [4][ 990/1109]	Loss 1.0643 (1.0797)	Accuracy 59.766 (59.883)
Epoch: [4][1000/1109]	Loss 1.0964 (1.0797)	Accuracy 57.422 (59.874)
Epoch: [4][1010/1109]	Loss 1.0117 (1.0795)	Accuracy 65.234 (59.887)
Epoch: [4][1020/1109]	Loss 1.0518 (1.0795)	Accuracy 58.594 (59.884)
Epoch: [4][1030/1109]	Loss 1.0331 (1.0796)	Accuracy 60.547 (59.875)
Epoch: [4][1040/1109]	Loss 1.0848 (1.0797)	Accuracy 59.766 (59.878)
Epoch: [4][1050/1109]	Loss 1.0545 (1.0796)	Accuracy 57.422 (59.875)
Epoch: [4][1060/1109]	Loss 1.2724 (1.0797)	Accuracy 50.000 (59.866)
Epoch: [4][1070/1109]	Loss 1.0807 (1.0798)	Accuracy 57.812 (59.859)
Epoch: [4][1080/1109]	Loss 1.1743 (1.0796)	Accuracy 56.250 (59.875)
Epoch: [4][1090/1109]	Loss 0.9824 (1.0792)	Accuracy 60.938 (59.886)
Epoch: [4][1100/1109]	Loss 1.0332 (1.0787)	Accuracy 61.328 (59.906)
Test: [ 0/14]	Loss 1.1005 (1.1005)	Accuracy 56.250 (56.250)
Test: [10/14]	Loss 1.7703 (1.0657)	Accuracy 48.438 (62.962)
 * Accuracy 60.560
Current best accuracy: 62.103458404541016
1041.7428107261658
Current learning rate: 0.001
Epoch: [5][   0/1109]	Loss 1.0605 (1.0605)	Accuracy 59.766 (59.766)
Epoch: [5][  10/1109]	Loss 1.1243 (1.0862)	Accuracy 57.031 (59.553)
Epoch: [5][  20/1109]	Loss 0.8606 (1.0612)	Accuracy 67.969 (60.454)
Epoch: [5][  30/1109]	Loss 1.0864 (1.0644)	Accuracy 58.984 (59.992)
Epoch: [5][  40/1109]	Loss 1.0142 (1.0584)	Accuracy 61.328 (60.499)
Epoch: [5][  50/1109]	Loss 1.0467 (1.0538)	Accuracy 62.500 (60.723)
Epoch: [5][  60/1109]	Loss 1.0230 (1.0509)	Accuracy 63.281 (60.963)
Epoch: [5][  70/1109]	Loss 1.0041 (1.0468)	Accuracy 60.938 (61.114)
Epoch: [5][  80/1109]	Loss 0.9944 (1.0417)	Accuracy 66.016 (61.372)
Epoch: [5][  90/1109]	Loss 0.9787 (1.0381)	Accuracy 62.109 (61.508)
Epoch: [5][ 100/1109]	Loss 0.9716 (1.0364)	Accuracy 62.500 (61.421)
Epoch: [5][ 110/1109]	Loss 1.0088 (1.0331)	Accuracy 66.016 (61.550)
Epoch: [5][ 120/1109]	Loss 1.1158 (1.0322)	Accuracy 62.500 (61.699)
Epoch: [5][ 130/1109]	Loss 0.9852 (1.0308)	Accuracy 62.891 (61.731)
Epoch: [5][ 140/1109]	Loss 0.9450 (1.0278)	Accuracy 65.234 (61.857)
Epoch: [5][ 150/1109]	Loss 1.0664 (1.0264)	Accuracy 62.109 (61.933)
Epoch: [5][ 160/1109]	Loss 1.1457 (1.0261)	Accuracy 59.375 (62.039)
Epoch: [5][ 170/1109]	Loss 1.1240 (1.0260)	Accuracy 56.641 (62.043)
Epoch: [5][ 180/1109]	Loss 1.0818 (1.0257)	Accuracy 58.203 (61.995)
Epoch: [5][ 190/1109]	Loss 1.0737 (1.0251)	Accuracy 60.156 (62.019)
Epoch: [5][ 200/1109]	Loss 0.9978 (1.0251)	Accuracy 62.891 (61.987)
Epoch: [5][ 210/1109]	Loss 1.0004 (1.0239)	Accuracy 61.719 (61.996)
Epoch: [5][ 220/1109]	Loss 0.9821 (1.0222)	Accuracy 62.500 (62.030)
Epoch: [5][ 230/1109]	Loss 1.0250 (1.0194)	Accuracy 62.500 (62.177)
Epoch: [5][ 240/1109]	Loss 1.0226 (1.0188)	Accuracy 59.375 (62.168)
Epoch: [5][ 250/1109]	Loss 0.9020 (1.0185)	Accuracy 66.797 (62.195)
Epoch: [5][ 260/1109]	Loss 0.9792 (1.0187)	Accuracy 63.672 (62.178)
Epoch: [5][ 270/1109]	Loss 1.0143 (1.0191)	Accuracy 61.328 (62.144)
Epoch: [5][ 280/1109]	Loss 1.0279 (1.0193)	Accuracy 62.109 (62.102)
Epoch: [5][ 290/1109]	Loss 1.0577 (1.0185)	Accuracy 60.156 (62.119)
Epoch: [5][ 300/1109]	Loss 0.9078 (1.0184)	Accuracy 63.672 (62.133)
Epoch: [5][ 310/1109]	Loss 1.0312 (1.0181)	Accuracy 60.938 (62.148)
Epoch: [5][ 320/1109]	Loss 1.0620 (1.0179)	Accuracy 61.328 (62.191)
Epoch: [5][ 330/1109]	Loss 0.9594 (1.0170)	Accuracy 65.234 (62.234)
Epoch: [5][ 340/1109]	Loss 0.9392 (1.0167)	Accuracy 62.109 (62.222)
Epoch: [5][ 350/1109]	Loss 1.0189 (1.0160)	Accuracy 59.375 (62.233)
Epoch: [5][ 360/1109]	Loss 0.8904 (1.0152)	Accuracy 66.406 (62.249)
Epoch: [5][ 370/1109]	Loss 0.9800 (1.0150)	Accuracy 65.234 (62.275)
Epoch: [5][ 380/1109]	Loss 1.0191 (1.0143)	Accuracy 64.062 (62.294)
Epoch: [5][ 390/1109]	Loss 0.9877 (1.0139)	Accuracy 62.891 (62.300)
Epoch: [5][ 400/1109]	Loss 1.0006 (1.0136)	Accuracy 62.109 (62.313)
Epoch: [5][ 410/1109]	Loss 1.1089 (1.0133)	Accuracy 58.984 (62.312)
Epoch: [5][ 420/1109]	Loss 1.0153 (1.0122)	Accuracy 62.109 (62.358)
Epoch: [5][ 430/1109]	Loss 0.9844 (1.0120)	Accuracy 61.719 (62.340)
Epoch: [5][ 440/1109]	Loss 0.9389 (1.0114)	Accuracy 63.281 (62.381)
Epoch: [5][ 450/1109]	Loss 0.9601 (1.0112)	Accuracy 68.750 (62.398)
Epoch: [5][ 460/1109]	Loss 1.0143 (1.0107)	Accuracy 61.328 (62.420)
Epoch: [5][ 470/1109]	Loss 0.9894 (1.0101)	Accuracy 59.375 (62.439)
Epoch: [5][ 480/1109]	Loss 1.0119 (1.0099)	Accuracy 62.500 (62.455)
Epoch: [5][ 490/1109]	Loss 0.9806 (1.0095)	Accuracy 62.891 (62.479)
Epoch: [5][ 500/1109]	Loss 1.0555 (1.0095)	Accuracy 62.500 (62.490)
Epoch: [5][ 510/1109]	Loss 0.9912 (1.0099)	Accuracy 60.938 (62.453)
Epoch: [5][ 520/1109]	Loss 1.2224 (1.0100)	Accuracy 57.031 (62.453)
Epoch: [5][ 530/1109]	Loss 1.0252 (1.0101)	Accuracy 63.672 (62.458)
Epoch: [5][ 540/1109]	Loss 0.9756 (1.0103)	Accuracy 61.719 (62.454)
Epoch: [5][ 550/1109]	Loss 1.0110 (1.0106)	Accuracy 63.281 (62.442)
Epoch: [5][ 560/1109]	Loss 0.9761 (1.0106)	Accuracy 64.453 (62.450)
Epoch: [5][ 570/1109]	Loss 0.8699 (1.0099)	Accuracy 64.453 (62.475)
Epoch: [5][ 580/1109]	Loss 1.0302 (1.0095)	Accuracy 62.500 (62.509)
Epoch: [5][ 590/1109]	Loss 0.9572 (1.0091)	Accuracy 61.719 (62.511)
Epoch: [5][ 600/1109]	Loss 0.9511 (1.0079)	Accuracy 66.016 (62.555)
Epoch: [5][ 610/1109]	Loss 0.9380 (1.0076)	Accuracy 68.750 (62.559)
Epoch: [5][ 620/1109]	Loss 0.8991 (1.0072)	Accuracy 67.969 (62.568)
Epoch: [5][ 630/1109]	Loss 0.9266 (1.0069)	Accuracy 66.016 (62.558)
Epoch: [5][ 640/1109]	Loss 1.0428 (1.0072)	Accuracy 57.812 (62.552)
Epoch: [5][ 650/1109]	Loss 0.9485 (1.0070)	Accuracy 66.016 (62.553)
Epoch: [5][ 660/1109]	Loss 0.9881 (1.0063)	Accuracy 61.719 (62.594)
Epoch: [5][ 670/1109]	Loss 0.9666 (1.0061)	Accuracy 61.719 (62.599)
Epoch: [5][ 680/1109]	Loss 0.9845 (1.0057)	Accuracy 62.500 (62.612)
Epoch: [5][ 690/1109]	Loss 0.9256 (1.0057)	Accuracy 66.406 (62.627)
Epoch: [5][ 700/1109]	Loss 0.9858 (1.0056)	Accuracy 62.109 (62.624)
Epoch: [5][ 710/1109]	Loss 0.9689 (1.0054)	Accuracy 64.062 (62.624)
Epoch: [5][ 720/1109]	Loss 0.8782 (1.0051)	Accuracy 66.406 (62.637)
Epoch: [5][ 730/1109]	Loss 0.9007 (1.0048)	Accuracy 67.578 (62.654)
Epoch: [5][ 740/1109]	Loss 0.9980 (1.0048)	Accuracy 63.281 (62.651)
Epoch: [5][ 750/1109]	Loss 1.0049 (1.0052)	Accuracy 62.500 (62.644)
Epoch: [5][ 760/1109]	Loss 1.0097 (1.0051)	Accuracy 61.328 (62.641)
Epoch: [5][ 770/1109]	Loss 1.0231 (1.0049)	Accuracy 60.547 (62.653)
Epoch: [5][ 780/1109]	Loss 1.1228 (1.0048)	Accuracy 54.688 (62.657)
Epoch: [5][ 790/1109]	Loss 0.9994 (1.0051)	Accuracy 60.156 (62.639)
Epoch: [5][ 800/1109]	Loss 0.9387 (1.0048)	Accuracy 67.578 (62.657)
Epoch: [5][ 810/1109]	Loss 1.0557 (1.0045)	Accuracy 58.594 (62.677)
Epoch: [5][ 820/1109]	Loss 1.0069 (1.0043)	Accuracy 64.453 (62.676)
Epoch: [5][ 830/1109]	Loss 0.9229 (1.0041)	Accuracy 66.016 (62.677)
Epoch: [5][ 840/1109]	Loss 0.9016 (1.0038)	Accuracy 70.312 (62.686)
Epoch: [5][ 850/1109]	Loss 1.0833 (1.0041)	Accuracy 61.328 (62.683)
Epoch: [5][ 860/1109]	Loss 0.9015 (1.0039)	Accuracy 67.188 (62.686)
Epoch: [5][ 870/1109]	Loss 1.0278 (1.0041)	Accuracy 64.453 (62.691)
Epoch: [5][ 880/1109]	Loss 0.9341 (1.0037)	Accuracy 64.062 (62.707)
Epoch: [5][ 890/1109]	Loss 1.0538 (1.0040)	Accuracy 60.938 (62.708)
Epoch: [5][ 900/1109]	Loss 1.0343 (1.0040)	Accuracy 58.984 (62.703)
Epoch: [5][ 910/1109]	Loss 1.0285 (1.0043)	Accuracy 61.719 (62.704)
Epoch: [5][ 920/1109]	Loss 0.9320 (1.0040)	Accuracy 64.062 (62.712)
Epoch: [5][ 930/1109]	Loss 1.0295 (1.0039)	Accuracy 61.719 (62.721)
Epoch: [5][ 940/1109]	Loss 1.0827 (1.0037)	Accuracy 57.031 (62.726)
Epoch: [5][ 950/1109]	Loss 0.9188 (1.0037)	Accuracy 64.453 (62.722)
Epoch: [5][ 960/1109]	Loss 1.0280 (1.0038)	Accuracy 60.938 (62.711)
Epoch: [5][ 970/1109]	Loss 0.9818 (1.0037)	Accuracy 64.062 (62.716)
Epoch: [5][ 980/1109]	Loss 0.9024 (1.0034)	Accuracy 66.016 (62.722)
Epoch: [5][ 990/1109]	Loss 0.9025 (1.0030)	Accuracy 67.578 (62.740)
Epoch: [5][1000/1109]	Loss 1.0246 (1.0029)	Accuracy 63.672 (62.759)
Epoch: [5][1010/1109]	Loss 1.0617 (1.0026)	Accuracy 58.594 (62.763)
Epoch: [5][1020/1109]	Loss 0.9996 (1.0025)	Accuracy 63.672 (62.777)
Epoch: [5][1030/1109]	Loss 0.9319 (1.0021)	Accuracy 64.062 (62.793)
Epoch: [5][1040/1109]	Loss 1.0467 (1.0021)	Accuracy 63.281 (62.799)
Epoch: [5][1050/1109]	Loss 0.9433 (1.0019)	Accuracy 66.406 (62.817)
Epoch: [5][1060/1109]	Loss 1.0679 (1.0016)	Accuracy 59.375 (62.831)
Epoch: [5][1070/1109]	Loss 0.8961 (1.0010)	Accuracy 67.969 (62.853)
Epoch: [5][1080/1109]	Loss 0.9765 (1.0008)	Accuracy 65.234 (62.873)
Epoch: [5][1090/1109]	Loss 1.0002 (1.0008)	Accuracy 63.281 (62.877)
Epoch: [5][1100/1109]	Loss 0.8754 (1.0005)	Accuracy 67.969 (62.897)
Test: [ 0/14]	Loss 0.9658 (0.9658)	Accuracy 67.969 (67.969)
Test: [10/14]	Loss 1.6515 (1.0088)	Accuracy 50.391 (64.631)
 * Accuracy 63.704
Current best accuracy: 63.70391845703125
937.7976839542389
Current learning rate: 0.001
Epoch: [6][   0/1109]	Loss 0.9562 (0.9562)	Accuracy 64.453 (64.453)
Epoch: [6][  10/1109]	Loss 0.9448 (0.9814)	Accuracy 64.844 (63.317)
Epoch: [6][  20/1109]	Loss 0.9384 (0.9702)	Accuracy 64.844 (63.932)
Epoch: [6][  30/1109]	Loss 1.0811 (0.9766)	Accuracy 58.984 (63.899)
Epoch: [6][  40/1109]	Loss 0.9322 (0.9746)	Accuracy 67.578 (64.005)
Epoch: [6][  50/1109]	Loss 0.9767 (0.9812)	Accuracy 63.281 (63.695)
Epoch: [6][  60/1109]	Loss 0.8759 (0.9786)	Accuracy 70.703 (63.794)
Epoch: [6][  70/1109]	Loss 0.8962 (0.9795)	Accuracy 66.797 (63.826)
Epoch: [6][  80/1109]	Loss 1.0652 (0.9787)	Accuracy 61.719 (63.812)
Epoch: [6][  90/1109]	Loss 0.9419 (0.9766)	Accuracy 65.234 (63.899)
Epoch: [6][ 100/1109]	Loss 0.9202 (0.9771)	Accuracy 64.453 (63.935)
Epoch: [6][ 110/1109]	Loss 0.9760 (0.9757)	Accuracy 66.406 (64.045)
Epoch: [6][ 120/1109]	Loss 1.0379 (0.9766)	Accuracy 63.672 (63.959)
Epoch: [6][ 130/1109]	Loss 1.1048 (0.9753)	Accuracy 60.156 (63.991)
Epoch: [6][ 140/1109]	Loss 0.9333 (0.9747)	Accuracy 65.625 (64.035)
Epoch: [6][ 150/1109]	Loss 1.1103 (0.9771)	Accuracy 59.766 (63.990)
Epoch: [6][ 160/1109]	Loss 1.0283 (0.9765)	Accuracy 61.328 (64.002)
Epoch: [6][ 170/1109]	Loss 1.0049 (0.9770)	Accuracy 62.109 (63.992)
Epoch: [6][ 180/1109]	Loss 0.9961 (0.9766)	Accuracy 58.594 (63.978)
Epoch: [6][ 190/1109]	Loss 1.0604 (0.9774)	Accuracy 58.203 (64.020)
Epoch: [6][ 200/1109]	Loss 0.9739 (0.9766)	Accuracy 62.109 (64.064)
Epoch: [6][ 210/1109]	Loss 1.0491 (0.9765)	Accuracy 60.547 (64.025)
Epoch: [6][ 220/1109]	Loss 1.1109 (0.9781)	Accuracy 58.594 (63.979)
Epoch: [6][ 230/1109]	Loss 1.0346 (0.9796)	Accuracy 60.547 (63.888)
Epoch: [6][ 240/1109]	Loss 0.9581 (0.9795)	Accuracy 64.062 (63.865)
Epoch: [6][ 250/1109]	Loss 0.9987 (0.9796)	Accuracy 65.625 (63.846)
Epoch: [6][ 260/1109]	Loss 0.9233 (0.9801)	Accuracy 65.234 (63.837)
Epoch: [6][ 270/1109]	Loss 0.9212 (0.9802)	Accuracy 66.406 (63.803)
Epoch: [6][ 280/1109]	Loss 0.9973 (0.9814)	Accuracy 62.891 (63.732)
Epoch: [6][ 290/1109]	Loss 0.9150 (0.9809)	Accuracy 69.141 (63.732)
Epoch: [6][ 300/1109]	Loss 0.9468 (0.9808)	Accuracy 64.062 (63.722)
Epoch: [6][ 310/1109]	Loss 0.9554 (0.9808)	Accuracy 66.406 (63.733)
Epoch: [6][ 320/1109]	Loss 0.9261 (0.9803)	Accuracy 64.453 (63.752)
Epoch: [6][ 330/1109]	Loss 0.9577 (0.9805)	Accuracy 60.547 (63.730)
Epoch: [6][ 340/1109]	Loss 0.9812 (0.9814)	Accuracy 58.984 (63.678)
Epoch: [6][ 350/1109]	Loss 1.0322 (0.9810)	Accuracy 57.031 (63.694)
Epoch: [6][ 360/1109]	Loss 0.9935 (0.9810)	Accuracy 63.672 (63.662)
Epoch: [6][ 370/1109]	Loss 0.9404 (0.9809)	Accuracy 64.453 (63.634)
Epoch: [6][ 380/1109]	Loss 1.0051 (0.9814)	Accuracy 63.281 (63.610)
Epoch: [6][ 390/1109]	Loss 0.9683 (0.9817)	Accuracy 64.453 (63.578)
Epoch: [6][ 400/1109]	Loss 0.9579 (0.9819)	Accuracy 66.797 (63.571)
Epoch: [6][ 410/1109]	Loss 0.9299 (0.9824)	Accuracy 67.969 (63.544)
Epoch: [6][ 420/1109]	Loss 1.0040 (0.9819)	Accuracy 63.672 (63.585)
Epoch: [6][ 430/1109]	Loss 0.9910 (0.9819)	Accuracy 64.062 (63.588)
Epoch: [6][ 440/1109]	Loss 0.9186 (0.9817)	Accuracy 67.188 (63.620)
Epoch: [6][ 450/1109]	Loss 1.0191 (0.9821)	Accuracy 63.281 (63.593)
Epoch: [6][ 460/1109]	Loss 0.9800 (0.9817)	Accuracy 62.109 (63.595)
Epoch: [6][ 470/1109]	Loss 0.9741 (0.9819)	Accuracy 63.672 (63.587)
Epoch: [6][ 480/1109]	Loss 0.8474 (0.9819)	Accuracy 68.750 (63.596)
Epoch: [6][ 490/1109]	Loss 0.9716 (0.9823)	Accuracy 64.453 (63.589)
Epoch: [6][ 500/1109]	Loss 0.9388 (0.9819)	Accuracy 68.750 (63.594)
Epoch: [6][ 510/1109]	Loss 1.0867 (0.9825)	Accuracy 59.766 (63.558)
Epoch: [6][ 520/1109]	Loss 0.9789 (0.9824)	Accuracy 60.156 (63.553)
Epoch: [6][ 530/1109]	Loss 0.9475 (0.9818)	Accuracy 63.281 (63.580)
Epoch: [6][ 540/1109]	Loss 1.0558 (0.9822)	Accuracy 60.156 (63.560)
Epoch: [6][ 550/1109]	Loss 0.9180 (0.9819)	Accuracy 67.578 (63.558)
Epoch: [6][ 560/1109]	Loss 0.8886 (0.9813)	Accuracy 68.750 (63.590)
Epoch: [6][ 570/1109]	Loss 1.0131 (0.9814)	Accuracy 62.109 (63.579)
Epoch: [6][ 580/1109]	Loss 0.9766 (0.9813)	Accuracy 61.719 (63.568)
Epoch: [6][ 590/1109]	Loss 0.9455 (0.9811)	Accuracy 67.188 (63.583)
Epoch: [6][ 600/1109]	Loss 1.0030 (0.9812)	Accuracy 66.016 (63.599)
Epoch: [6][ 610/1109]	Loss 1.0410 (0.9810)	Accuracy 62.109 (63.605)
Epoch: [6][ 620/1109]	Loss 1.0057 (0.9808)	Accuracy 62.891 (63.625)
Epoch: [6][ 630/1109]	Loss 1.0079 (0.9806)	Accuracy 60.156 (63.630)
Epoch: [6][ 640/1109]	Loss 0.9310 (0.9801)	Accuracy 69.531 (63.653)
Epoch: [6][ 650/1109]	Loss 1.0083 (0.9803)	Accuracy 60.156 (63.637)
Epoch: [6][ 660/1109]	Loss 0.9924 (0.9800)	Accuracy 62.109 (63.642)
Epoch: [6][ 670/1109]	Loss 1.0236 (0.9798)	Accuracy 64.062 (63.652)
Epoch: [6][ 680/1109]	Loss 0.9227 (0.9794)	Accuracy 62.109 (63.657)
Epoch: [6][ 690/1109]	Loss 1.0105 (0.9795)	Accuracy 62.109 (63.646)
Epoch: [6][ 700/1109]	Loss 0.9954 (0.9795)	Accuracy 62.500 (63.643)
Epoch: [6][ 710/1109]	Loss 1.0226 (0.9795)	Accuracy 66.406 (63.650)
Epoch: [6][ 720/1109]	Loss 0.9642 (0.9799)	Accuracy 65.625 (63.630)
Epoch: [6][ 730/1109]	Loss 1.1285 (0.9800)	Accuracy 58.594 (63.638)
Epoch: [6][ 740/1109]	Loss 0.9699 (0.9798)	Accuracy 64.453 (63.643)
Epoch: [6][ 750/1109]	Loss 1.0422 (0.9800)	Accuracy 63.281 (63.638)
Epoch: [6][ 760/1109]	Loss 1.0251 (0.9799)	Accuracy 64.844 (63.644)
Epoch: [6][ 770/1109]	Loss 0.9916 (0.9797)	Accuracy 61.328 (63.651)
Epoch: [6][ 780/1109]	Loss 0.9197 (0.9793)	Accuracy 64.844 (63.663)
Epoch: [6][ 790/1109]	Loss 0.9369 (0.9791)	Accuracy 62.109 (63.653)
Epoch: [6][ 800/1109]	Loss 0.9262 (0.9788)	Accuracy 63.281 (63.663)
Epoch: [6][ 810/1109]	Loss 1.0064 (0.9786)	Accuracy 62.109 (63.672)
Epoch: [6][ 820/1109]	Loss 0.8971 (0.9784)	Accuracy 66.016 (63.680)
Epoch: [6][ 830/1109]	Loss 0.9971 (0.9781)	Accuracy 68.750 (63.696)
Epoch: [6][ 840/1109]	Loss 0.9393 (0.9778)	Accuracy 64.453 (63.709)
Epoch: [6][ 850/1109]	Loss 1.0249 (0.9781)	Accuracy 62.109 (63.697)
Epoch: [6][ 860/1109]	Loss 1.0172 (0.9782)	Accuracy 60.547 (63.694)
Epoch: [6][ 870/1109]	Loss 0.9458 (0.9782)	Accuracy 65.234 (63.696)
Epoch: [6][ 880/1109]	Loss 1.0009 (0.9782)	Accuracy 61.328 (63.683)
Epoch: [6][ 890/1109]	Loss 0.9535 (0.9783)	Accuracy 63.672 (63.683)
Epoch: [6][ 900/1109]	Loss 0.9727 (0.9781)	Accuracy 61.328 (63.698)
Epoch: [6][ 910/1109]	Loss 0.9216 (0.9781)	Accuracy 67.578 (63.698)
Epoch: [6][ 920/1109]	Loss 1.0167 (0.9783)	Accuracy 59.766 (63.688)
Epoch: [6][ 930/1109]	Loss 0.9536 (0.9778)	Accuracy 62.891 (63.705)
Epoch: [6][ 940/1109]	Loss 0.9406 (0.9777)	Accuracy 67.188 (63.713)
Epoch: [6][ 950/1109]	Loss 0.9636 (0.9776)	Accuracy 62.500 (63.717)
Epoch: [6][ 960/1109]	Loss 1.0372 (0.9772)	Accuracy 62.500 (63.734)
Epoch: [6][ 970/1109]	Loss 0.9762 (0.9770)	Accuracy 62.891 (63.744)
Epoch: [6][ 980/1109]	Loss 0.9964 (0.9768)	Accuracy 61.719 (63.759)
Epoch: [6][ 990/1109]	Loss 0.9972 (0.9766)	Accuracy 61.328 (63.769)
Epoch: [6][1000/1109]	Loss 0.9614 (0.9768)	Accuracy 63.672 (63.760)
Epoch: [6][1010/1109]	Loss 0.9829 (0.9768)	Accuracy 61.719 (63.755)
Epoch: [6][1020/1109]	Loss 0.9376 (0.9767)	Accuracy 66.406 (63.756)
Epoch: [6][1030/1109]	Loss 0.9624 (0.9766)	Accuracy 60.938 (63.759)
Epoch: [6][1040/1109]	Loss 0.9832 (0.9766)	Accuracy 62.891 (63.766)
Epoch: [6][1050/1109]	Loss 1.0117 (0.9766)	Accuracy 60.938 (63.762)
Epoch: [6][1060/1109]	Loss 1.0734 (0.9768)	Accuracy 57.422 (63.750)
Epoch: [6][1070/1109]	Loss 0.8660 (0.9768)	Accuracy 65.625 (63.740)
Epoch: [6][1080/1109]	Loss 1.0458 (0.9771)	Accuracy 60.547 (63.741)
Epoch: [6][1090/1109]	Loss 1.0067 (0.9772)	Accuracy 60.938 (63.741)
Epoch: [6][1100/1109]	Loss 0.9875 (0.9771)	Accuracy 64.453 (63.734)
Test: [ 0/14]	Loss 1.0223 (1.0223)	Accuracy 64.844 (64.844)
Test: [10/14]	Loss 1.6278 (0.9850)	Accuracy 53.125 (65.909)
 * Accuracy 64.533
Current best accuracy: 64.53272247314453
1168.2567489147186
Current learning rate: 0.001
Epoch: [7][   0/1109]	Loss 0.9643 (0.9643)	Accuracy 62.109 (62.109)
Epoch: [7][  10/1109]	Loss 1.0792 (1.0026)	Accuracy 58.984 (62.784)
Epoch: [7][  20/1109]	Loss 0.9047 (0.9739)	Accuracy 66.797 (64.267)
Epoch: [7][  30/1109]	Loss 1.0100 (0.9694)	Accuracy 63.281 (64.642)
Epoch: [7][  40/1109]	Loss 0.9822 (0.9642)	Accuracy 64.062 (64.701)
Epoch: [7][  50/1109]	Loss 0.9703 (0.9657)	Accuracy 65.234 (64.545)
Epoch: [7][  60/1109]	Loss 0.9543 (0.9652)	Accuracy 63.281 (64.383)
Epoch: [7][  70/1109]	Loss 1.0882 (0.9676)	Accuracy 57.031 (64.277)
Epoch: [7][  80/1109]	Loss 0.9465 (0.9678)	Accuracy 66.016 (64.284)
Epoch: [7][  90/1109]	Loss 1.0095 (0.9718)	Accuracy 62.891 (64.140)
Epoch: [7][ 100/1109]	Loss 1.0635 (0.9727)	Accuracy 59.375 (64.008)
Epoch: [7][ 110/1109]	Loss 0.8264 (0.9706)	Accuracy 70.312 (64.140)
Epoch: [7][ 120/1109]	Loss 1.1080 (0.9712)	Accuracy 58.594 (64.072)
Epoch: [7][ 130/1109]	Loss 0.8880 (0.9706)	Accuracy 65.234 (64.122)
Epoch: [7][ 140/1109]	Loss 0.9389 (0.9699)	Accuracy 61.719 (64.148)
Epoch: [7][ 150/1109]	Loss 1.0367 (0.9733)	Accuracy 58.203 (64.052)
Epoch: [7][ 160/1109]	Loss 1.0461 (0.9737)	Accuracy 58.594 (63.973)
Epoch: [7][ 170/1109]	Loss 0.9081 (0.9728)	Accuracy 65.234 (63.983)
Epoch: [7][ 180/1109]	Loss 1.0536 (0.9747)	Accuracy 59.375 (63.836)
Epoch: [7][ 190/1109]	Loss 0.9424 (0.9753)	Accuracy 61.328 (63.825)
Epoch: [7][ 200/1109]	Loss 0.8988 (0.9744)	Accuracy 70.312 (63.872)
Epoch: [7][ 210/1109]	Loss 0.9368 (0.9727)	Accuracy 66.406 (63.948)
Epoch: [7][ 220/1109]	Loss 0.9598 (0.9723)	Accuracy 67.578 (63.976)
Epoch: [7][ 230/1109]	Loss 1.0733 (0.9721)	Accuracy 63.672 (63.973)
Epoch: [7][ 240/1109]	Loss 0.9455 (0.9712)	Accuracy 64.453 (64.025)
Epoch: [7][ 250/1109]	Loss 0.8850 (0.9711)	Accuracy 67.578 (64.013)
Epoch: [7][ 260/1109]	Loss 0.9581 (0.9708)	Accuracy 64.844 (64.042)
Epoch: [7][ 270/1109]	Loss 0.9131 (0.9694)	Accuracy 67.578 (64.124)
Epoch: [7][ 280/1109]	Loss 0.9537 (0.9695)	Accuracy 62.891 (64.085)
Epoch: [7][ 290/1109]	Loss 1.0627 (0.9706)	Accuracy 57.422 (64.029)
Epoch: [7][ 300/1109]	Loss 0.8689 (0.9695)	Accuracy 70.312 (64.094)
Epoch: [7][ 310/1109]	Loss 0.9409 (0.9698)	Accuracy 66.016 (64.041)
Epoch: [7][ 320/1109]	Loss 0.9666 (0.9693)	Accuracy 61.328 (64.048)
Epoch: [7][ 330/1109]	Loss 1.0192 (0.9687)	Accuracy 60.938 (64.080)
Epoch: [7][ 340/1109]	Loss 1.0553 (0.9687)	Accuracy 58.984 (64.060)
Epoch: [7][ 350/1109]	Loss 0.9969 (0.9685)	Accuracy 60.938 (64.081)
Epoch: [7][ 360/1109]	Loss 0.9770 (0.9680)	Accuracy 66.016 (64.114)
Epoch: [7][ 370/1109]	Loss 1.0383 (0.9676)	Accuracy 59.375 (64.117)
Epoch: [7][ 380/1109]	Loss 0.9729 (0.9673)	Accuracy 62.891 (64.143)
Epoch: [7][ 390/1109]	Loss 0.9498 (0.9668)	Accuracy 65.234 (64.186)
Epoch: [7][ 400/1109]	Loss 0.8884 (0.9661)	Accuracy 67.188 (64.183)
Epoch: [7][ 410/1109]	Loss 0.9135 (0.9662)	Accuracy 64.844 (64.149)
Epoch: [7][ 420/1109]	Loss 0.9401 (0.9659)	Accuracy 65.234 (64.154)
Epoch: [7][ 430/1109]	Loss 0.9978 (0.9664)	Accuracy 58.984 (64.131)
Epoch: [7][ 440/1109]	Loss 0.9094 (0.9663)	Accuracy 64.844 (64.121)
Epoch: [7][ 450/1109]	Loss 0.9362 (0.9664)	Accuracy 66.016 (64.137)
Epoch: [7][ 460/1109]	Loss 0.9669 (0.9666)	Accuracy 64.062 (64.149)
Epoch: [7][ 470/1109]	Loss 1.0240 (0.9668)	Accuracy 61.719 (64.151)
Epoch: [7][ 480/1109]	Loss 0.9458 (0.9667)	Accuracy 64.844 (64.150)
Epoch: [7][ 490/1109]	Loss 1.0335 (0.9674)	Accuracy 62.109 (64.124)
Epoch: [7][ 500/1109]	Loss 1.0404 (0.9675)	Accuracy 62.891 (64.125)
Epoch: [7][ 510/1109]	Loss 0.9170 (0.9677)	Accuracy 62.109 (64.099)
Epoch: [7][ 520/1109]	Loss 1.0370 (0.9673)	Accuracy 64.453 (64.123)
Epoch: [7][ 530/1109]	Loss 0.9410 (0.9673)	Accuracy 66.406 (64.119)
Epoch: [7][ 540/1109]	Loss 0.9811 (0.9667)	Accuracy 64.062 (64.158)
Epoch: [7][ 550/1109]	Loss 0.9732 (0.9673)	Accuracy 63.281 (64.140)
Epoch: [7][ 560/1109]	Loss 1.0406 (0.9671)	Accuracy 58.203 (64.135)
Epoch: [7][ 570/1109]	Loss 0.9302 (0.9670)	Accuracy 64.844 (64.154)
Epoch: [7][ 580/1109]	Loss 1.0806 (0.9673)	Accuracy 60.156 (64.128)
Epoch: [7][ 590/1109]	Loss 0.9425 (0.9679)	Accuracy 63.281 (64.117)
Epoch: [7][ 600/1109]	Loss 0.9661 (0.9679)	Accuracy 65.234 (64.129)
Epoch: [7][ 610/1109]	Loss 0.9981 (0.9676)	Accuracy 63.672 (64.169)
Epoch: [7][ 620/1109]	Loss 0.9666 (0.9676)	Accuracy 62.109 (64.151)
Epoch: [7][ 630/1109]	Loss 1.0478 (0.9676)	Accuracy 58.594 (64.142)
Epoch: [7][ 640/1109]	Loss 0.9587 (0.9676)	Accuracy 64.844 (64.144)
Epoch: [7][ 650/1109]	Loss 0.9519 (0.9681)	Accuracy 63.281 (64.105)
Epoch: [7][ 660/1109]	Loss 1.0343 (0.9679)	Accuracy 62.109 (64.128)
Epoch: [7][ 670/1109]	Loss 0.8855 (0.9677)	Accuracy 67.578 (64.141)
Epoch: [7][ 680/1109]	Loss 0.9887 (0.9677)	Accuracy 61.328 (64.134)
Epoch: [7][ 690/1109]	Loss 0.8463 (0.9677)	Accuracy 68.750 (64.130)
Epoch: [7][ 700/1109]	Loss 0.9381 (0.9674)	Accuracy 65.625 (64.143)
Epoch: [7][ 710/1109]	Loss 1.0286 (0.9675)	Accuracy 63.672 (64.130)
Epoch: [7][ 720/1109]	Loss 0.9948 (0.9675)	Accuracy 62.891 (64.129)
Epoch: [7][ 730/1109]	Loss 1.0086 (0.9675)	Accuracy 62.109 (64.130)
Epoch: [7][ 740/1109]	Loss 0.9445 (0.9674)	Accuracy 65.625 (64.126)
Epoch: [7][ 750/1109]	Loss 0.9617 (0.9678)	Accuracy 60.938 (64.115)
Epoch: [7][ 760/1109]	Loss 0.9814 (0.9676)	Accuracy 63.672 (64.110)
Epoch: [7][ 770/1109]	Loss 0.9513 (0.9678)	Accuracy 65.625 (64.104)
Epoch: [7][ 780/1109]	Loss 0.9522 (0.9676)	Accuracy 66.797 (64.124)
Epoch: [7][ 790/1109]	Loss 0.9657 (0.9677)	Accuracy 67.578 (64.130)
Epoch: [7][ 800/1109]	Loss 1.0397 (0.9676)	Accuracy 59.375 (64.130)
Epoch: [7][ 810/1109]	Loss 0.9906 (0.9674)	Accuracy 63.672 (64.141)
Epoch: [7][ 820/1109]	Loss 0.9145 (0.9673)	Accuracy 66.406 (64.137)
Epoch: [7][ 830/1109]	Loss 0.8869 (0.9670)	Accuracy 68.750 (64.146)
Epoch: [7][ 840/1109]	Loss 0.9589 (0.9672)	Accuracy 63.281 (64.141)
Epoch: [7][ 850/1109]	Loss 0.9401 (0.9671)	Accuracy 66.406 (64.157)
Epoch: [7][ 860/1109]	Loss 0.9550 (0.9669)	Accuracy 64.844 (64.168)
Epoch: [7][ 870/1109]	Loss 0.9839 (0.9669)	Accuracy 66.797 (64.172)
Epoch: [7][ 880/1109]	Loss 1.0899 (0.9672)	Accuracy 60.156 (64.178)
Epoch: [7][ 890/1109]	Loss 0.8867 (0.9671)	Accuracy 67.969 (64.188)
Epoch: [7][ 900/1109]	Loss 0.9162 (0.9669)	Accuracy 66.797 (64.198)
Epoch: [7][ 910/1109]	Loss 0.8781 (0.9667)	Accuracy 68.359 (64.214)
Epoch: [7][ 920/1109]	Loss 0.9469 (0.9666)	Accuracy 64.453 (64.225)
Epoch: [7][ 930/1109]	Loss 0.9345 (0.9663)	Accuracy 67.188 (64.248)
Epoch: [7][ 940/1109]	Loss 1.0089 (0.9662)	Accuracy 63.672 (64.254)
Epoch: [7][ 950/1109]	Loss 0.8882 (0.9661)	Accuracy 66.016 (64.257)
Epoch: [7][ 960/1109]	Loss 1.0523 (0.9657)	Accuracy 60.547 (64.261)
Epoch: [7][ 970/1109]	Loss 0.9492 (0.9654)	Accuracy 65.625 (64.276)
Epoch: [7][ 980/1109]	Loss 0.9855 (0.9655)	Accuracy 60.547 (64.283)
Epoch: [7][ 990/1109]	Loss 0.9911 (0.9654)	Accuracy 62.500 (64.269)
Epoch: [7][1000/1109]	Loss 1.0087 (0.9654)	Accuracy 62.891 (64.270)
Epoch: [7][1010/1109]	Loss 0.9854 (0.9653)	Accuracy 60.938 (64.275)
Epoch: [7][1020/1109]	Loss 0.9202 (0.9653)	Accuracy 67.578 (64.278)
Epoch: [7][1030/1109]	Loss 0.9523 (0.9651)	Accuracy 63.281 (64.271)
Epoch: [7][1040/1109]	Loss 0.9065 (0.9652)	Accuracy 68.750 (64.269)
Epoch: [7][1050/1109]	Loss 0.9503 (0.9653)	Accuracy 64.453 (64.270)
Epoch: [7][1060/1109]	Loss 0.9177 (0.9651)	Accuracy 65.625 (64.277)
Epoch: [7][1070/1109]	Loss 0.9760 (0.9652)	Accuracy 64.062 (64.276)
Epoch: [7][1080/1109]	Loss 0.8820 (0.9651)	Accuracy 67.188 (64.280)
Epoch: [7][1090/1109]	Loss 0.9299 (0.9652)	Accuracy 67.188 (64.277)
Epoch: [7][1100/1109]	Loss 0.9433 (0.9651)	Accuracy 65.234 (64.286)
Test: [ 0/14]	Loss 0.9829 (0.9829)	Accuracy 64.062 (64.062)
Test: [10/14]	Loss 2.0238 (1.0323)	Accuracy 46.094 (64.489)
 * Accuracy 63.790
Current best accuracy: 64.53272247314453
799.6374139785767
Current learning rate: 0.001
Epoch: [8][   0/1109]	Loss 0.9513 (0.9513)	Accuracy 63.281 (63.281)
Epoch: [8][  10/1109]	Loss 0.9680 (0.9589)	Accuracy 64.453 (64.418)
Epoch: [8][  20/1109]	Loss 1.0687 (0.9584)	Accuracy 58.984 (64.397)
Epoch: [8][  30/1109]	Loss 0.9030 (0.9649)	Accuracy 68.750 (64.226)
Epoch: [8][  40/1109]	Loss 0.8645 (0.9687)	Accuracy 67.969 (64.053)
Epoch: [8][  50/1109]	Loss 0.9755 (0.9640)	Accuracy 62.500 (64.231)
Epoch: [8][  60/1109]	Loss 0.8427 (0.9592)	Accuracy 68.750 (64.434)
Epoch: [8][  70/1109]	Loss 0.9335 (0.9582)	Accuracy 64.453 (64.398)
Epoch: [8][  80/1109]	Loss 0.8769 (0.9549)	Accuracy 67.969 (64.506)
Epoch: [8][  90/1109]	Loss 1.0389 (0.9568)	Accuracy 58.203 (64.522)
Epoch: [8][ 100/1109]	Loss 1.0717 (0.9602)	Accuracy 56.641 (64.318)
Epoch: [8][ 110/1109]	Loss 0.9460 (0.9594)	Accuracy 61.328 (64.369)
Epoch: [8][ 120/1109]	Loss 0.9507 (0.9606)	Accuracy 66.797 (64.347)
Epoch: [8][ 130/1109]	Loss 1.0198 (0.9613)	Accuracy 64.453 (64.349)
Epoch: [8][ 140/1109]	Loss 0.9077 (0.9624)	Accuracy 64.844 (64.342)
Epoch: [8][ 150/1109]	Loss 1.0474 (0.9624)	Accuracy 63.672 (64.376)
Epoch: [8][ 160/1109]	Loss 0.9199 (0.9613)	Accuracy 68.750 (64.451)
Epoch: [8][ 170/1109]	Loss 0.9344 (0.9624)	Accuracy 63.672 (64.522)
Epoch: [8][ 180/1109]	Loss 0.9552 (0.9613)	Accuracy 66.016 (64.591)
Epoch: [8][ 190/1109]	Loss 0.9222 (0.9602)	Accuracy 66.016 (64.607)
Epoch: [8][ 200/1109]	Loss 0.9209 (0.9586)	Accuracy 66.797 (64.694)
Epoch: [8][ 210/1109]	Loss 1.0612 (0.9599)	Accuracy 63.281 (64.609)
Epoch: [8][ 220/1109]	Loss 0.9652 (0.9600)	Accuracy 64.453 (64.602)
Epoch: [8][ 230/1109]	Loss 0.9717 (0.9586)	Accuracy 63.281 (64.600)
Epoch: [8][ 240/1109]	Loss 1.0229 (0.9593)	Accuracy 57.422 (64.578)
Epoch: [8][ 250/1109]	Loss 0.8469 (0.9585)	Accuracy 66.406 (64.645)
Epoch: [8][ 260/1109]	Loss 0.8955 (0.9577)	Accuracy 69.141 (64.663)
Epoch: [8][ 270/1109]	Loss 0.9235 (0.9573)	Accuracy 65.234 (64.691)
Epoch: [8][ 280/1109]	Loss 0.9857 (0.9564)	Accuracy 62.891 (64.719)
Epoch: [8][ 290/1109]	Loss 0.9529 (0.9567)	Accuracy 62.500 (64.692)
Epoch: [8][ 300/1109]	Loss 1.0109 (0.9564)	Accuracy 64.062 (64.691)
Epoch: [8][ 310/1109]	Loss 0.9431 (0.9564)	Accuracy 63.281 (64.712)
Epoch: [8][ 320/1109]	Loss 0.8623 (0.9554)	Accuracy 67.969 (64.739)
Epoch: [8][ 330/1109]	Loss 0.9542 (0.9556)	Accuracy 67.578 (64.732)
Epoch: [8][ 340/1109]	Loss 0.9295 (0.9549)	Accuracy 64.844 (64.777)
Epoch: [8][ 350/1109]	Loss 1.0288 (0.9548)	Accuracy 63.281 (64.763)
Epoch: [8][ 360/1109]	Loss 0.8968 (0.9545)	Accuracy 66.406 (64.790)
Epoch: [8][ 370/1109]	Loss 0.9769 (0.9541)	Accuracy 66.797 (64.823)
Epoch: [8][ 380/1109]	Loss 0.9604 (0.9550)	Accuracy 67.188 (64.838)
Epoch: [8][ 390/1109]	Loss 1.0184 (0.9551)	Accuracy 58.984 (64.827)
Epoch: [8][ 400/1109]	Loss 0.8568 (0.9544)	Accuracy 67.969 (64.852)
Epoch: [8][ 410/1109]	Loss 0.8915 (0.9540)	Accuracy 66.016 (64.839)
Epoch: [8][ 420/1109]	Loss 0.9338 (0.9537)	Accuracy 67.188 (64.855)
Epoch: [8][ 430/1109]	Loss 0.9657 (0.9542)	Accuracy 66.016 (64.828)
Epoch: [8][ 440/1109]	Loss 1.0064 (0.9549)	Accuracy 63.672 (64.809)
Epoch: [8][ 450/1109]	Loss 1.0389 (0.9550)	Accuracy 58.594 (64.800)
Epoch: [8][ 460/1109]	Loss 0.9603 (0.9549)	Accuracy 64.453 (64.805)
Epoch: [8][ 470/1109]	Loss 0.9181 (0.9545)	Accuracy 66.406 (64.816)
Epoch: [8][ 480/1109]	Loss 1.0694 (0.9552)	Accuracy 57.031 (64.787)
Epoch: [8][ 490/1109]	Loss 1.0086 (0.9559)	Accuracy 61.328 (64.740)
Epoch: [8][ 500/1109]	Loss 0.8697 (0.9560)	Accuracy 67.969 (64.754)
Epoch: [8][ 510/1109]	Loss 0.9260 (0.9563)	Accuracy 66.406 (64.737)
Epoch: [8][ 520/1109]	Loss 0.9742 (0.9564)	Accuracy 64.453 (64.730)
Epoch: [8][ 530/1109]	Loss 1.0924 (0.9562)	Accuracy 60.156 (64.738)
Epoch: [8][ 540/1109]	Loss 0.9557 (0.9558)	Accuracy 63.672 (64.745)
Epoch: [8][ 550/1109]	Loss 0.9479 (0.9557)	Accuracy 61.719 (64.737)
Epoch: [8][ 560/1109]	Loss 0.9734 (0.9559)	Accuracy 65.625 (64.737)
Epoch: [8][ 570/1109]	Loss 0.8311 (0.9552)	Accuracy 69.141 (64.768)
Epoch: [8][ 580/1109]	Loss 1.0676 (0.9556)	Accuracy 59.766 (64.742)
Epoch: [8][ 590/1109]	Loss 0.9772 (0.9559)	Accuracy 63.281 (64.716)
Epoch: [8][ 600/1109]	Loss 0.9056 (0.9565)	Accuracy 69.141 (64.703)
Epoch: [8][ 610/1109]	Loss 0.9814 (0.9566)	Accuracy 66.797 (64.706)
Epoch: [8][ 620/1109]	Loss 0.9363 (0.9570)	Accuracy 62.109 (64.687)
Epoch: [8][ 630/1109]	Loss 0.9004 (0.9571)	Accuracy 66.797 (64.694)
Epoch: [8][ 640/1109]	Loss 0.9218 (0.9575)	Accuracy 67.578 (64.682)
Epoch: [8][ 650/1109]	Loss 0.8655 (0.9575)	Accuracy 69.922 (64.684)
Epoch: [8][ 660/1109]	Loss 0.9990 (0.9573)	Accuracy 60.547 (64.687)
Epoch: [8][ 670/1109]	Loss 0.9463 (0.9570)	Accuracy 64.844 (64.708)
Epoch: [8][ 680/1109]	Loss 0.9026 (0.9570)	Accuracy 67.578 (64.709)
Epoch: [8][ 690/1109]	Loss 0.9910 (0.9570)	Accuracy 63.672 (64.702)
Epoch: [8][ 700/1109]	Loss 0.9452 (0.9565)	Accuracy 66.797 (64.731)
Epoch: [8][ 710/1109]	Loss 1.0057 (0.9569)	Accuracy 61.328 (64.711)
Epoch: [8][ 720/1109]	Loss 0.9674 (0.9572)	Accuracy 64.844 (64.701)
Epoch: [8][ 730/1109]	Loss 0.9168 (0.9574)	Accuracy 65.234 (64.683)
Epoch: [8][ 740/1109]	Loss 0.9707 (0.9574)	Accuracy 66.797 (64.680)
Epoch: [8][ 750/1109]	Loss 0.9874 (0.9572)	Accuracy 62.109 (64.677)
Epoch: [8][ 760/1109]	Loss 1.0236 (0.9572)	Accuracy 61.719 (64.669)
Epoch: [8][ 770/1109]	Loss 0.9221 (0.9572)	Accuracy 67.969 (64.668)
Epoch: [8][ 780/1109]	Loss 1.0229 (0.9572)	Accuracy 61.719 (64.663)
Epoch: [8][ 790/1109]	Loss 1.0037 (0.9572)	Accuracy 62.891 (64.669)
Epoch: [8][ 800/1109]	Loss 0.9419 (0.9572)	Accuracy 66.016 (64.676)
Epoch: [8][ 810/1109]	Loss 0.9184 (0.9569)	Accuracy 64.844 (64.686)
Epoch: [8][ 820/1109]	Loss 0.9329 (0.9571)	Accuracy 66.406 (64.681)
Epoch: [8][ 830/1109]	Loss 0.9769 (0.9567)	Accuracy 65.625 (64.708)
Epoch: [8][ 840/1109]	Loss 0.9355 (0.9566)	Accuracy 64.453 (64.715)
Epoch: [8][ 850/1109]	Loss 0.8230 (0.9566)	Accuracy 69.531 (64.717)
Epoch: [8][ 860/1109]	Loss 0.9954 (0.9565)	Accuracy 62.891 (64.721)
Epoch: [8][ 870/1109]	Loss 0.9706 (0.9564)	Accuracy 64.453 (64.727)
Epoch: [8][ 880/1109]	Loss 0.8766 (0.9560)	Accuracy 66.406 (64.743)
Epoch: [8][ 890/1109]	Loss 0.9503 (0.9558)	Accuracy 68.359 (64.753)
Epoch: [8][ 900/1109]	Loss 1.0261 (0.9560)	Accuracy 62.500 (64.757)
Epoch: [8][ 910/1109]	Loss 1.0277 (0.9560)	Accuracy 62.500 (64.768)
Epoch: [8][ 920/1109]	Loss 1.0134 (0.9561)	Accuracy 64.844 (64.769)
Epoch: [8][ 930/1109]	Loss 0.9589 (0.9559)	Accuracy 62.109 (64.770)
Epoch: [8][ 940/1109]	Loss 0.9837 (0.9560)	Accuracy 62.891 (64.753)
Epoch: [8][ 950/1109]	Loss 0.9793 (0.9559)	Accuracy 60.938 (64.753)
Epoch: [8][ 960/1109]	Loss 0.8584 (0.9561)	Accuracy 69.922 (64.745)
Epoch: [8][ 970/1109]	Loss 0.9835 (0.9560)	Accuracy 64.844 (64.747)
Epoch: [8][ 980/1109]	Loss 0.9580 (0.9562)	Accuracy 66.406 (64.741)
Epoch: [8][ 990/1109]	Loss 0.9524 (0.9561)	Accuracy 64.062 (64.737)
Epoch: [8][1000/1109]	Loss 0.9629 (0.9563)	Accuracy 65.234 (64.726)
Epoch: [8][1010/1109]	Loss 0.9969 (0.9562)	Accuracy 60.938 (64.720)
Epoch: [8][1020/1109]	Loss 1.1059 (0.9564)	Accuracy 62.109 (64.721)
Epoch: [8][1030/1109]	Loss 0.8825 (0.9560)	Accuracy 68.359 (64.735)
Epoch: [8][1040/1109]	Loss 0.9917 (0.9561)	Accuracy 62.109 (64.729)
Epoch: [8][1050/1109]	Loss 0.9249 (0.9559)	Accuracy 66.016 (64.724)
Epoch: [8][1060/1109]	Loss 1.0151 (0.9560)	Accuracy 60.156 (64.712)
Epoch: [8][1070/1109]	Loss 0.9108 (0.9558)	Accuracy 68.750 (64.731)
Epoch: [8][1080/1109]	Loss 0.9863 (0.9557)	Accuracy 64.453 (64.734)
Epoch: [8][1090/1109]	Loss 0.8678 (0.9557)	Accuracy 67.969 (64.731)
Epoch: [8][1100/1109]	Loss 0.9478 (0.9557)	Accuracy 65.234 (64.732)
Test: [ 0/14]	Loss 0.9327 (0.9327)	Accuracy 66.016 (66.016)
Test: [10/14]	Loss 1.9024 (1.0150)	Accuracy 49.609 (65.305)
 * Accuracy 63.675
Current best accuracy: 64.53272247314453
799.7478711605072
Current learning rate: 0.001
Epoch: [9][   0/1109]	Loss 0.9911 (0.9911)	Accuracy 62.109 (62.109)
Epoch: [9][  10/1109]	Loss 0.9616 (0.9539)	Accuracy 63.672 (65.341)
Epoch: [9][  20/1109]	Loss 0.9483 (0.9626)	Accuracy 66.406 (64.955)
Epoch: [9][  30/1109]	Loss 1.0195 (0.9676)	Accuracy 64.844 (64.680)
Epoch: [9][  40/1109]	Loss 0.9767 (0.9703)	Accuracy 64.062 (64.672)
Epoch: [9][  50/1109]	Loss 0.9376 (0.9699)	Accuracy 66.406 (64.499)
Epoch: [9][  60/1109]	Loss 0.8271 (0.9640)	Accuracy 71.094 (64.626)
Epoch: [9][  70/1109]	Loss 0.9824 (0.9646)	Accuracy 62.109 (64.574)
Epoch: [9][  80/1109]	Loss 1.0200 (0.9636)	Accuracy 64.062 (64.434)
Epoch: [9][  90/1109]	Loss 0.9443 (0.9608)	Accuracy 69.141 (64.663)
Epoch: [9][ 100/1109]	Loss 0.9064 (0.9593)	Accuracy 68.750 (64.701)
Epoch: [9][ 110/1109]	Loss 0.8786 (0.9602)	Accuracy 67.188 (64.569)
Epoch: [9][ 120/1109]	Loss 0.9956 (0.9597)	Accuracy 61.719 (64.602)
Epoch: [9][ 130/1109]	Loss 0.8846 (0.9581)	Accuracy 64.844 (64.695)
Epoch: [9][ 140/1109]	Loss 0.9086 (0.9585)	Accuracy 67.969 (64.647)
Epoch: [9][ 150/1109]	Loss 0.9805 (0.9593)	Accuracy 66.797 (64.575)
Epoch: [9][ 160/1109]	Loss 1.0334 (0.9580)	Accuracy 57.812 (64.642)
Epoch: [9][ 170/1109]	Loss 0.9700 (0.9569)	Accuracy 63.672 (64.693)
Epoch: [9][ 180/1109]	Loss 0.9951 (0.9565)	Accuracy 68.359 (64.710)
Epoch: [9][ 190/1109]	Loss 0.9582 (0.9563)	Accuracy 64.453 (64.668)
Epoch: [9][ 200/1109]	Loss 1.0380 (0.9572)	Accuracy 62.500 (64.667)
Epoch: [9][ 210/1109]	Loss 1.0295 (0.9573)	Accuracy 60.156 (64.685)
Epoch: [9][ 220/1109]	Loss 0.8876 (0.9563)	Accuracy 68.359 (64.739)
Epoch: [9][ 230/1109]	Loss 0.9057 (0.9574)	Accuracy 61.719 (64.680)
Epoch: [9][ 240/1109]	Loss 0.9463 (0.9569)	Accuracy 64.062 (64.677)
Epoch: [9][ 250/1109]	Loss 0.9205 (0.9562)	Accuracy 63.281 (64.696)
Epoch: [9][ 260/1109]	Loss 0.9372 (0.9548)	Accuracy 64.844 (64.773)
Epoch: [9][ 270/1109]	Loss 0.8011 (0.9538)	Accuracy 69.531 (64.815)
Epoch: [9][ 280/1109]	Loss 0.9375 (0.9543)	Accuracy 64.453 (64.790)
Epoch: [9][ 290/1109]	Loss 0.9922 (0.9547)	Accuracy 61.719 (64.790)
Epoch: [9][ 300/1109]	Loss 0.9514 (0.9545)	Accuracy 64.844 (64.793)
Epoch: [9][ 310/1109]	Loss 1.1018 (0.9551)	Accuracy 64.844 (64.773)
Epoch: [9][ 320/1109]	Loss 0.9638 (0.9552)	Accuracy 65.234 (64.788)
Epoch: [9][ 330/1109]	Loss 0.9534 (0.9548)	Accuracy 64.844 (64.812)
Epoch: [9][ 340/1109]	Loss 0.9245 (0.9553)	Accuracy 67.188 (64.821)
Epoch: [9][ 350/1109]	Loss 1.0120 (0.9548)	Accuracy 63.281 (64.801)
Epoch: [9][ 360/1109]	Loss 0.9100 (0.9545)	Accuracy 66.016 (64.772)
Epoch: [9][ 370/1109]	Loss 0.9260 (0.9554)	Accuracy 62.891 (64.718)
Epoch: [9][ 380/1109]	Loss 1.0290 (0.9561)	Accuracy 62.891 (64.692)
Epoch: [9][ 390/1109]	Loss 0.9601 (0.9566)	Accuracy 61.328 (64.655)
Epoch: [9][ 400/1109]	Loss 0.9719 (0.9559)	Accuracy 66.797 (64.681)
Epoch: [9][ 410/1109]	Loss 0.9351 (0.9556)	Accuracy 64.844 (64.689)
Epoch: [9][ 420/1109]	Loss 0.9699 (0.9549)	Accuracy 64.062 (64.739)
Epoch: [9][ 430/1109]	Loss 0.8894 (0.9549)	Accuracy 68.359 (64.759)
Epoch: [9][ 440/1109]	Loss 0.9537 (0.9556)	Accuracy 66.406 (64.708)
Epoch: [9][ 450/1109]	Loss 0.9887 (0.9556)	Accuracy 63.672 (64.700)
Epoch: [9][ 460/1109]	Loss 0.9199 (0.9555)	Accuracy 67.188 (64.711)
Epoch: [9][ 470/1109]	Loss 0.9937 (0.9558)	Accuracy 64.062 (64.702)
Epoch: [9][ 480/1109]	Loss 1.0214 (0.9556)	Accuracy 62.109 (64.715)
Epoch: [9][ 490/1109]	Loss 0.9044 (0.9553)	Accuracy 68.750 (64.747)
Epoch: [9][ 500/1109]	Loss 1.0187 (0.9553)	Accuracy 60.156 (64.762)
Epoch: [9][ 510/1109]	Loss 0.8693 (0.9556)	Accuracy 68.359 (64.753)
Epoch: [9][ 520/1109]	Loss 0.8937 (0.9553)	Accuracy 67.969 (64.771)
Epoch: [9][ 530/1109]	Loss 0.9150 (0.9547)	Accuracy 65.625 (64.783)
Epoch: [9][ 540/1109]	Loss 1.0301 (0.9542)	Accuracy 59.375 (64.799)
Epoch: [9][ 550/1109]	Loss 1.0147 (0.9547)	Accuracy 62.500 (64.772)
Epoch: [9][ 560/1109]	Loss 0.9919 (0.9548)	Accuracy 59.375 (64.762)
Epoch: [9][ 570/1109]	Loss 0.9837 (0.9550)	Accuracy 63.281 (64.756)
Epoch: [9][ 580/1109]	Loss 0.9668 (0.9550)	Accuracy 63.281 (64.759)
Epoch: [9][ 590/1109]	Loss 0.9912 (0.9548)	Accuracy 63.281 (64.762)
Epoch: [9][ 600/1109]	Loss 1.0072 (0.9544)	Accuracy 60.938 (64.776)
Epoch: [9][ 610/1109]	Loss 0.8834 (0.9540)	Accuracy 63.672 (64.795)
Epoch: [9][ 620/1109]	Loss 0.9974 (0.9541)	Accuracy 59.375 (64.788)
Epoch: [9][ 630/1109]	Loss 0.9752 (0.9540)	Accuracy 60.156 (64.778)
Epoch: [9][ 640/1109]	Loss 1.0236 (0.9540)	Accuracy 61.719 (64.780)
Epoch: [9][ 650/1109]	Loss 0.9368 (0.9540)	Accuracy 60.547 (64.781)
Epoch: [9][ 660/1109]	Loss 0.9040 (0.9544)	Accuracy 67.578 (64.769)
Epoch: [9][ 670/1109]	Loss 0.9231 (0.9542)	Accuracy 65.625 (64.772)
Epoch: [9][ 680/1109]	Loss 0.9501 (0.9544)	Accuracy 66.016 (64.766)
Epoch: [9][ 690/1109]	Loss 0.9912 (0.9541)	Accuracy 61.328 (64.786)
Epoch: [9][ 700/1109]	Loss 0.9523 (0.9538)	Accuracy 63.281 (64.799)
Epoch: [9][ 710/1109]	Loss 0.8256 (0.9536)	Accuracy 69.531 (64.804)
Epoch: [9][ 720/1109]	Loss 0.8743 (0.9535)	Accuracy 70.703 (64.822)
Epoch: [9][ 730/1109]	Loss 1.0798 (0.9533)	Accuracy 62.500 (64.827)
Epoch: [9][ 740/1109]	Loss 1.0220 (0.9535)	Accuracy 60.547 (64.813)
Epoch: [9][ 750/1109]	Loss 0.9383 (0.9532)	Accuracy 66.016 (64.810)
Epoch: [9][ 760/1109]	Loss 0.8354 (0.9528)	Accuracy 67.969 (64.824)
Epoch: [9][ 770/1109]	Loss 0.9499 (0.9523)	Accuracy 68.750 (64.855)
Epoch: [9][ 780/1109]	Loss 0.9476 (0.9520)	Accuracy 68.359 (64.860)
Epoch: [9][ 790/1109]	Loss 0.8828 (0.9519)	Accuracy 63.672 (64.848)
Epoch: [9][ 800/1109]	Loss 0.9477 (0.9523)	Accuracy 62.109 (64.827)
Epoch: [9][ 810/1109]	Loss 0.9376 (0.9521)	Accuracy 66.406 (64.841)
Epoch: [9][ 820/1109]	Loss 0.9880 (0.9521)	Accuracy 60.547 (64.844)
Epoch: [9][ 830/1109]	Loss 0.8807 (0.9524)	Accuracy 70.703 (64.843)
Epoch: [9][ 840/1109]	Loss 0.9130 (0.9522)	Accuracy 66.406 (64.852)
Epoch: [9][ 850/1109]	Loss 0.9745 (0.9524)	Accuracy 63.672 (64.843)
Epoch: [9][ 860/1109]	Loss 1.0479 (0.9527)	Accuracy 62.500 (64.832)
Epoch: [9][ 870/1109]	Loss 0.8509 (0.9522)	Accuracy 69.922 (64.840)
Epoch: [9][ 880/1109]	Loss 0.9598 (0.9523)	Accuracy 65.625 (64.841)
Epoch: [9][ 890/1109]	Loss 0.8777 (0.9521)	Accuracy 70.312 (64.851)
Epoch: [9][ 900/1109]	Loss 0.8889 (0.9519)	Accuracy 68.750 (64.868)
Epoch: [9][ 910/1109]	Loss 1.0154 (0.9525)	Accuracy 61.328 (64.833)
Epoch: [9][ 920/1109]	Loss 0.9925 (0.9524)	Accuracy 63.281 (64.837)
Epoch: [9][ 930/1109]	Loss 0.9968 (0.9522)	Accuracy 62.500 (64.837)
Epoch: [9][ 940/1109]	Loss 0.9355 (0.9521)	Accuracy 62.109 (64.839)
Epoch: [9][ 950/1109]	Loss 1.0575 (0.9522)	Accuracy 60.938 (64.826)
Epoch: [9][ 960/1109]	Loss 0.8690 (0.9519)	Accuracy 67.969 (64.839)
Epoch: [9][ 970/1109]	Loss 0.8036 (0.9517)	Accuracy 70.312 (64.844)
Epoch: [9][ 980/1109]	Loss 0.9619 (0.9517)	Accuracy 66.406 (64.839)
Epoch: [9][ 990/1109]	Loss 0.8800 (0.9517)	Accuracy 67.969 (64.842)
Epoch: [9][1000/1109]	Loss 1.0152 (0.9516)	Accuracy 62.500 (64.848)
Epoch: [9][1010/1109]	Loss 0.9288 (0.9516)	Accuracy 66.016 (64.855)
Epoch: [9][1020/1109]	Loss 1.0098 (0.9520)	Accuracy 59.766 (64.838)
Epoch: [9][1030/1109]	Loss 0.9552 (0.9519)	Accuracy 63.281 (64.842)
Epoch: [9][1040/1109]	Loss 0.9175 (0.9521)	Accuracy 65.625 (64.822)
Epoch: [9][1050/1109]	Loss 0.9899 (0.9519)	Accuracy 63.672 (64.826)
Epoch: [9][1060/1109]	Loss 0.9613 (0.9520)	Accuracy 60.156 (64.822)
Epoch: [9][1070/1109]	Loss 0.8528 (0.9519)	Accuracy 68.750 (64.831)
Epoch: [9][1080/1109]	Loss 0.9457 (0.9518)	Accuracy 63.281 (64.841)
Epoch: [9][1090/1109]	Loss 1.0513 (0.9519)	Accuracy 63.281 (64.842)
Epoch: [9][1100/1109]	Loss 0.8992 (0.9518)	Accuracy 67.578 (64.846)
Test: [ 0/14]	Loss 0.9681 (0.9681)	Accuracy 65.234 (65.234)
Test: [10/14]	Loss 2.0645 (1.0532)	Accuracy 44.922 (64.453)
 * Accuracy 63.447
Current best accuracy: 64.53272247314453
802.4491243362427
Current learning rate: 0.00010000000000000002
Epoch: [10][   0/1109]	Loss 0.9510 (0.9510)	Accuracy 63.672 (63.672)
Epoch: [10][  10/1109]	Loss 0.8964 (0.9453)	Accuracy 67.969 (65.803)
Epoch: [10][  20/1109]	Loss 0.9107 (0.9564)	Accuracy 66.016 (65.048)
Epoch: [10][  30/1109]	Loss 1.0161 (0.9597)	Accuracy 62.891 (64.743)
Epoch: [10][  40/1109]	Loss 0.9342 (0.9519)	Accuracy 67.188 (65.187)
Epoch: [10][  50/1109]	Loss 1.0064 (0.9500)	Accuracy 62.109 (65.127)
Epoch: [10][  60/1109]	Loss 0.9431 (0.9489)	Accuracy 66.406 (65.049)
Epoch: [10][  70/1109]	Loss 0.8490 (0.9432)	Accuracy 67.969 (65.333)
Epoch: [10][  80/1109]	Loss 0.9436 (0.9436)	Accuracy 63.672 (65.278)
Epoch: [10][  90/1109]	Loss 0.8124 (0.9388)	Accuracy 71.484 (65.389)
Epoch: [10][ 100/1109]	Loss 0.9380 (0.9408)	Accuracy 66.797 (65.308)
Epoch: [10][ 110/1109]	Loss 0.9427 (0.9393)	Accuracy 66.016 (65.326)
Epoch: [10][ 120/1109]	Loss 0.8958 (0.9387)	Accuracy 68.750 (65.386)
Epoch: [10][ 130/1109]	Loss 0.8606 (0.9393)	Accuracy 67.578 (65.360)
Epoch: [10][ 140/1109]	Loss 1.0055 (0.9394)	Accuracy 64.844 (65.348)
Epoch: [10][ 150/1109]	Loss 0.9764 (0.9398)	Accuracy 62.500 (65.338)
Epoch: [10][ 160/1109]	Loss 0.9425 (0.9409)	Accuracy 64.062 (65.314)
Epoch: [10][ 170/1109]	Loss 0.9540 (0.9411)	Accuracy 60.938 (65.298)
Epoch: [10][ 180/1109]	Loss 0.8939 (0.9418)	Accuracy 67.578 (65.280)
Epoch: [10][ 190/1109]	Loss 0.9749 (0.9415)	Accuracy 62.891 (65.273)
Epoch: [10][ 200/1109]	Loss 0.8837 (0.9412)	Accuracy 67.188 (65.299)
Epoch: [10][ 210/1109]	Loss 0.8997 (0.9416)	Accuracy 67.188 (65.264)
Epoch: [10][ 220/1109]	Loss 0.9276 (0.9402)	Accuracy 64.453 (65.298)
Epoch: [10][ 230/1109]	Loss 0.8709 (0.9387)	Accuracy 67.969 (65.397)
Epoch: [10][ 240/1109]	Loss 0.8482 (0.9387)	Accuracy 67.578 (65.427)
Epoch: [10][ 250/1109]	Loss 0.9903 (0.9388)	Accuracy 63.281 (65.402)
Epoch: [10][ 260/1109]	Loss 0.9451 (0.9377)	Accuracy 64.844 (65.423)
Epoch: [10][ 270/1109]	Loss 0.9219 (0.9377)	Accuracy 64.062 (65.438)
Epoch: [10][ 280/1109]	Loss 0.9308 (0.9374)	Accuracy 64.844 (65.448)
Epoch: [10][ 290/1109]	Loss 0.9495 (0.9366)	Accuracy 65.625 (65.463)
Epoch: [10][ 300/1109]	Loss 1.0333 (0.9373)	Accuracy 62.500 (65.443)
Epoch: [10][ 310/1109]	Loss 0.8625 (0.9372)	Accuracy 68.750 (65.459)
Epoch: [10][ 320/1109]	Loss 0.9147 (0.9369)	Accuracy 67.188 (65.453)
Epoch: [10][ 330/1109]	Loss 0.8887 (0.9380)	Accuracy 69.922 (65.428)
Epoch: [10][ 340/1109]	Loss 0.9147 (0.9381)	Accuracy 65.234 (65.433)
Epoch: [10][ 350/1109]	Loss 0.9744 (0.9382)	Accuracy 63.672 (65.417)
Epoch: [10][ 360/1109]	Loss 0.9069 (0.9391)	Accuracy 65.625 (65.404)
Epoch: [10][ 370/1109]	Loss 0.9544 (0.9392)	Accuracy 67.188 (65.399)
Epoch: [10][ 380/1109]	Loss 0.9358 (0.9391)	Accuracy 67.188 (65.411)
Epoch: [10][ 390/1109]	Loss 0.9155 (0.9393)	Accuracy 64.844 (65.384)
Epoch: [10][ 400/1109]	Loss 0.9908 (0.9403)	Accuracy 66.406 (65.381)
Epoch: [10][ 410/1109]	Loss 0.9910 (0.9403)	Accuracy 61.719 (65.405)
Epoch: [10][ 420/1109]	Loss 0.9860 (0.9404)	Accuracy 61.328 (65.389)
Epoch: [10][ 430/1109]	Loss 0.8536 (0.9404)	Accuracy 68.750 (65.374)
Epoch: [10][ 440/1109]	Loss 1.0184 (0.9401)	Accuracy 64.062 (65.400)
Epoch: [10][ 450/1109]	Loss 0.9113 (0.9397)	Accuracy 66.016 (65.375)
Epoch: [10][ 460/1109]	Loss 0.9709 (0.9397)	Accuracy 62.500 (65.385)
Epoch: [10][ 470/1109]	Loss 0.9351 (0.9396)	Accuracy 64.453 (65.373)
Epoch: [10][ 480/1109]	Loss 1.0677 (0.9404)	Accuracy 61.328 (65.348)
Epoch: [10][ 490/1109]	Loss 0.9910 (0.9407)	Accuracy 64.062 (65.343)
Epoch: [10][ 500/1109]	Loss 0.8350 (0.9407)	Accuracy 67.969 (65.347)
Epoch: [10][ 510/1109]	Loss 0.9768 (0.9406)	Accuracy 64.453 (65.354)
Epoch: [10][ 520/1109]	Loss 0.9115 (0.9412)	Accuracy 66.797 (65.332)
Epoch: [10][ 530/1109]	Loss 0.9078 (0.9413)	Accuracy 67.578 (65.328)
Epoch: [10][ 540/1109]	Loss 0.9437 (0.9412)	Accuracy 66.406 (65.341)
Epoch: [10][ 550/1109]	Loss 1.0060 (0.9418)	Accuracy 60.938 (65.317)
Epoch: [10][ 560/1109]	Loss 1.0099 (0.9422)	Accuracy 60.938 (65.303)
Epoch: [10][ 570/1109]	Loss 1.0289 (0.9424)	Accuracy 65.625 (65.298)
Epoch: [10][ 580/1109]	Loss 1.0041 (0.9423)	Accuracy 62.109 (65.299)
Epoch: [10][ 590/1109]	Loss 0.9609 (0.9423)	Accuracy 63.672 (65.291)
Epoch: [10][ 600/1109]	Loss 0.8578 (0.9418)	Accuracy 65.234 (65.299)
Epoch: [10][ 610/1109]	Loss 0.9255 (0.9415)	Accuracy 66.406 (65.303)
Epoch: [10][ 620/1109]	Loss 0.9045 (0.9420)	Accuracy 70.312 (65.307)
Epoch: [10][ 630/1109]	Loss 0.9891 (0.9418)	Accuracy 67.188 (65.331)
Epoch: [10][ 640/1109]	Loss 0.8961 (0.9417)	Accuracy 70.312 (65.345)
Epoch: [10][ 650/1109]	Loss 0.8581 (0.9419)	Accuracy 68.750 (65.345)
Epoch: [10][ 660/1109]	Loss 0.9425 (0.9416)	Accuracy 67.188 (65.371)
Epoch: [10][ 670/1109]	Loss 0.9848 (0.9422)	Accuracy 64.062 (65.357)
Epoch: [10][ 680/1109]	Loss 1.0023 (0.9423)	Accuracy 63.672 (65.342)
Epoch: [10][ 690/1109]	Loss 0.9218 (0.9419)	Accuracy 65.625 (65.367)
Epoch: [10][ 700/1109]	Loss 0.9888 (0.9421)	Accuracy 64.844 (65.352)
Epoch: [10][ 710/1109]	Loss 0.9357 (0.9419)	Accuracy 66.406 (65.338)
Epoch: [10][ 720/1109]	Loss 0.9351 (0.9418)	Accuracy 60.156 (65.349)
Epoch: [10][ 730/1109]	Loss 0.8038 (0.9415)	Accuracy 71.875 (65.379)
Epoch: [10][ 740/1109]	Loss 0.9438 (0.9415)	Accuracy 63.281 (65.371)
Epoch: [10][ 750/1109]	Loss 0.9217 (0.9414)	Accuracy 67.188 (65.383)
Epoch: [10][ 760/1109]	Loss 0.8909 (0.9411)	Accuracy 66.797 (65.407)
Epoch: [10][ 770/1109]	Loss 0.8375 (0.9409)	Accuracy 73.438 (65.422)
Epoch: [10][ 780/1109]	Loss 0.9268 (0.9409)	Accuracy 66.016 (65.423)
Epoch: [10][ 790/1109]	Loss 0.9184 (0.9406)	Accuracy 67.188 (65.441)
Epoch: [10][ 800/1109]	Loss 1.0633 (0.9407)	Accuracy 60.938 (65.433)
Epoch: [10][ 810/1109]	Loss 0.9457 (0.9403)	Accuracy 62.891 (65.440)
Epoch: [10][ 820/1109]	Loss 1.0410 (0.9407)	Accuracy 60.156 (65.414)
Epoch: [10][ 830/1109]	Loss 0.8899 (0.9407)	Accuracy 69.922 (65.421)
Epoch: [10][ 840/1109]	Loss 0.8991 (0.9407)	Accuracy 64.844 (65.416)
Epoch: [10][ 850/1109]	Loss 0.9213 (0.9407)	Accuracy 68.359 (65.418)
Epoch: [10][ 860/1109]	Loss 0.9750 (0.9408)	Accuracy 64.844 (65.418)
Epoch: [10][ 870/1109]	Loss 0.9545 (0.9408)	Accuracy 65.234 (65.415)
Epoch: [10][ 880/1109]	Loss 0.8439 (0.9408)	Accuracy 67.188 (65.414)
Epoch: [10][ 890/1109]	Loss 0.9190 (0.9407)	Accuracy 66.797 (65.418)
Epoch: [10][ 900/1109]	Loss 0.9747 (0.9408)	Accuracy 61.719 (65.404)
Epoch: [10][ 910/1109]	Loss 0.9589 (0.9411)	Accuracy 62.109 (65.381)
Epoch: [10][ 920/1109]	Loss 0.9159 (0.9416)	Accuracy 64.453 (65.359)
Epoch: [10][ 930/1109]	Loss 0.9628 (0.9415)	Accuracy 65.234 (65.368)
Epoch: [10][ 940/1109]	Loss 0.8902 (0.9414)	Accuracy 67.969 (65.364)
Epoch: [10][ 950/1109]	Loss 0.8766 (0.9414)	Accuracy 67.578 (65.364)
Epoch: [10][ 960/1109]	Loss 0.8549 (0.9413)	Accuracy 68.359 (65.379)
Epoch: [10][ 970/1109]	Loss 0.9601 (0.9414)	Accuracy 68.359 (65.374)
Epoch: [10][ 980/1109]	Loss 0.9412 (0.9413)	Accuracy 61.719 (65.371)
Epoch: [10][ 990/1109]	Loss 0.9827 (0.9417)	Accuracy 64.062 (65.361)
Epoch: [10][1000/1109]	Loss 0.9534 (0.9419)	Accuracy 60.547 (65.341)
Epoch: [10][1010/1109]	Loss 0.8719 (0.9418)	Accuracy 65.625 (65.348)
Epoch: [10][1020/1109]	Loss 0.8723 (0.9415)	Accuracy 67.188 (65.350)
Epoch: [10][1030/1109]	Loss 0.8018 (0.9413)	Accuracy 70.703 (65.364)
Epoch: [10][1040/1109]	Loss 0.9052 (0.9411)	Accuracy 66.016 (65.369)
Epoch: [10][1050/1109]	Loss 0.9032 (0.9410)	Accuracy 65.625 (65.374)
Epoch: [10][1060/1109]	Loss 0.9786 (0.9411)	Accuracy 62.891 (65.370)
Epoch: [10][1070/1109]	Loss 0.8928 (0.9410)	Accuracy 67.188 (65.373)
Epoch: [10][1080/1109]	Loss 1.0918 (0.9413)	Accuracy 55.078 (65.356)
Epoch: [10][1090/1109]	Loss 0.8428 (0.9413)	Accuracy 70.312 (65.361)
Epoch: [10][1100/1109]	Loss 0.9706 (0.9413)	Accuracy 66.016 (65.362)
Test: [ 0/14]	Loss 0.9753 (0.9753)	Accuracy 64.453 (64.453)
Test: [10/14]	Loss 1.9980 (1.0450)	Accuracy 49.219 (64.666)
 * Accuracy 63.704
Current best accuracy: 64.53272247314453
811.3952674865723
Current learning rate: 0.00010000000000000002
Epoch: [11][   0/1109]	Loss 1.0334 (1.0334)	Accuracy 61.328 (61.328)
Epoch: [11][  10/1109]	Loss 1.1166 (0.9701)	Accuracy 59.766 (64.489)
Epoch: [11][  20/1109]	Loss 0.8830 (0.9650)	Accuracy 64.844 (64.528)
Epoch: [11][  30/1109]	Loss 0.8879 (0.9532)	Accuracy 71.484 (65.146)
Epoch: [11][  40/1109]	Loss 1.0005 (0.9510)	Accuracy 66.406 (64.920)
Epoch: [11][  50/1109]	Loss 0.9571 (0.9524)	Accuracy 66.406 (64.683)
Epoch: [11][  60/1109]	Loss 0.9269 (0.9511)	Accuracy 66.797 (64.869)
Epoch: [11][  70/1109]	Loss 0.9263 (0.9437)	Accuracy 65.625 (65.284)
Epoch: [11][  80/1109]	Loss 0.9946 (0.9459)	Accuracy 64.844 (65.220)
Epoch: [11][  90/1109]	Loss 1.0441 (0.9494)	Accuracy 61.328 (65.084)
Epoch: [11][ 100/1109]	Loss 0.9864 (0.9478)	Accuracy 63.672 (65.184)
Epoch: [11][ 110/1109]	Loss 0.8678 (0.9447)	Accuracy 67.578 (65.308)
Epoch: [11][ 120/1109]	Loss 0.9080 (0.9457)	Accuracy 65.625 (65.280)
Epoch: [11][ 130/1109]	Loss 0.9875 (0.9466)	Accuracy 62.109 (65.252)
Epoch: [11][ 140/1109]	Loss 0.9401 (0.9441)	Accuracy 65.625 (65.320)
Epoch: [11][ 150/1109]	Loss 0.8680 (0.9435)	Accuracy 67.188 (65.203)
Epoch: [11][ 160/1109]	Loss 0.8740 (0.9429)	Accuracy 69.141 (65.213)
Epoch: [11][ 170/1109]	Loss 0.8673 (0.9430)	Accuracy 71.094 (65.205)
Epoch: [11][ 180/1109]	Loss 0.9340 (0.9425)	Accuracy 66.406 (65.262)
Epoch: [11][ 190/1109]	Loss 0.9007 (0.9423)	Accuracy 69.141 (65.263)
Epoch: [11][ 200/1109]	Loss 0.9748 (0.9430)	Accuracy 68.359 (65.291)
Epoch: [11][ 210/1109]	Loss 0.9719 (0.9432)	Accuracy 62.109 (65.286)
Epoch: [11][ 220/1109]	Loss 0.9364 (0.9430)	Accuracy 65.234 (65.268)
Epoch: [11][ 230/1109]	Loss 0.9550 (0.9423)	Accuracy 63.281 (65.305)
Epoch: [11][ 240/1109]	Loss 0.9034 (0.9416)	Accuracy 64.844 (65.340)
Epoch: [11][ 250/1109]	Loss 1.0823 (0.9412)	Accuracy 57.422 (65.336)
Epoch: [11][ 260/1109]	Loss 0.8764 (0.9402)	Accuracy 70.703 (65.411)
Epoch: [11][ 270/1109]	Loss 0.9646 (0.9409)	Accuracy 63.672 (65.387)
Epoch: [11][ 280/1109]	Loss 0.9337 (0.9415)	Accuracy 65.625 (65.343)
Epoch: [11][ 290/1109]	Loss 0.8879 (0.9428)	Accuracy 65.234 (65.283)
Epoch: [11][ 300/1109]	Loss 0.9794 (0.9429)	Accuracy 64.844 (65.299)
Epoch: [11][ 310/1109]	Loss 1.0469 (0.9430)	Accuracy 62.109 (65.297)
Epoch: [11][ 320/1109]	Loss 0.9018 (0.9424)	Accuracy 69.141 (65.358)
Epoch: [11][ 330/1109]	Loss 0.9151 (0.9416)	Accuracy 66.797 (65.398)
Epoch: [11][ 340/1109]	Loss 0.9281 (0.9418)	Accuracy 64.062 (65.390)
Epoch: [11][ 350/1109]	Loss 1.0279 (0.9421)	Accuracy 61.328 (65.361)
Epoch: [11][ 360/1109]	Loss 0.9510 (0.9405)	Accuracy 64.453 (65.430)
Epoch: [11][ 370/1109]	Loss 0.8290 (0.9399)	Accuracy 68.359 (65.445)
Epoch: [11][ 380/1109]	Loss 1.0103 (0.9397)	Accuracy 63.281 (65.469)
Epoch: [11][ 390/1109]	Loss 0.9484 (0.9400)	Accuracy 66.016 (65.453)
Epoch: [11][ 400/1109]	Loss 1.0450 (0.9405)	Accuracy 59.375 (65.413)
Epoch: [11][ 410/1109]	Loss 1.0364 (0.9400)	Accuracy 62.500 (65.412)
Epoch: [11][ 420/1109]	Loss 0.9089 (0.9398)	Accuracy 67.188 (65.410)
Epoch: [11][ 430/1109]	Loss 0.9808 (0.9396)	Accuracy 62.891 (65.423)
Epoch: [11][ 440/1109]	Loss 0.9277 (0.9401)	Accuracy 64.453 (65.381)
Epoch: [11][ 450/1109]	Loss 0.9355 (0.9399)	Accuracy 63.672 (65.384)
Epoch: [11][ 460/1109]	Loss 1.0450 (0.9396)	Accuracy 59.766 (65.380)
Epoch: [11][ 470/1109]	Loss 0.8908 (0.9395)	Accuracy 67.188 (65.396)
Epoch: [11][ 480/1109]	Loss 1.0408 (0.9394)	Accuracy 65.234 (65.407)
Epoch: [11][ 490/1109]	Loss 0.9791 (0.9391)	Accuracy 65.234 (65.429)
Epoch: [11][ 500/1109]	Loss 0.8841 (0.9394)	Accuracy 64.844 (65.430)
Epoch: [11][ 510/1109]	Loss 0.8696 (0.9390)	Accuracy 67.578 (65.430)
Epoch: [11][ 520/1109]	Loss 1.0022 (0.9389)	Accuracy 64.453 (65.441)
Epoch: [11][ 530/1109]	Loss 0.9964 (0.9387)	Accuracy 62.500 (65.443)
Epoch: [11][ 540/1109]	Loss 0.9726 (0.9391)	Accuracy 63.672 (65.428)
Epoch: [11][ 550/1109]	Loss 0.9911 (0.9389)	Accuracy 63.281 (65.444)
Epoch: [11][ 560/1109]	Loss 0.9365 (0.9387)	Accuracy 66.406 (65.467)
Epoch: [11][ 570/1109]	Loss 0.8919 (0.9383)	Accuracy 69.531 (65.494)
Epoch: [11][ 580/1109]	Loss 0.8852 (0.9384)	Accuracy 69.141 (65.506)
Epoch: [11][ 590/1109]	Loss 0.9609 (0.9383)	Accuracy 62.891 (65.510)
Epoch: [11][ 600/1109]	Loss 0.9366 (0.9381)	Accuracy 64.453 (65.529)
Epoch: [11][ 610/1109]	Loss 1.0289 (0.9386)	Accuracy 60.547 (65.512)
Epoch: [11][ 620/1109]	Loss 0.9750 (0.9383)	Accuracy 63.672 (65.519)
Epoch: [11][ 630/1109]	Loss 0.8975 (0.9375)	Accuracy 63.672 (65.541)
Epoch: [11][ 640/1109]	Loss 0.9996 (0.9375)	Accuracy 63.672 (65.531)
Epoch: [11][ 650/1109]	Loss 0.9086 (0.9378)	Accuracy 67.578 (65.520)
Epoch: [11][ 660/1109]	Loss 1.0013 (0.9379)	Accuracy 64.453 (65.524)
Epoch: [11][ 670/1109]	Loss 0.9272 (0.9379)	Accuracy 67.188 (65.521)
Epoch: [11][ 680/1109]	Loss 0.8988 (0.9379)	Accuracy 67.188 (65.537)
Epoch: [11][ 690/1109]	Loss 0.9482 (0.9380)	Accuracy 66.406 (65.523)
Epoch: [11][ 700/1109]	Loss 0.9094 (0.9380)	Accuracy 65.234 (65.525)
Epoch: [11][ 710/1109]	Loss 0.9788 (0.9381)	Accuracy 64.453 (65.529)
Epoch: [11][ 720/1109]	Loss 1.0763 (0.9381)	Accuracy 60.547 (65.531)
Epoch: [11][ 730/1109]	Loss 0.8482 (0.9378)	Accuracy 67.578 (65.533)
Epoch: [11][ 740/1109]	Loss 1.0358 (0.9378)	Accuracy 60.938 (65.540)
Epoch: [11][ 750/1109]	Loss 0.9874 (0.9382)	Accuracy 63.281 (65.524)
Epoch: [11][ 760/1109]	Loss 1.1415 (0.9381)	Accuracy 60.547 (65.533)
Epoch: [11][ 770/1109]	Loss 0.9319 (0.9383)	Accuracy 65.625 (65.513)
Epoch: [11][ 780/1109]	Loss 1.0446 (0.9386)	Accuracy 61.328 (65.498)
Epoch: [11][ 790/1109]	Loss 0.9470 (0.9388)	Accuracy 65.234 (65.489)
Epoch: [11][ 800/1109]	Loss 0.9692 (0.9388)	Accuracy 64.844 (65.492)
Epoch: [11][ 810/1109]	Loss 1.0048 (0.9389)	Accuracy 64.062 (65.489)
Epoch: [11][ 820/1109]	Loss 0.9545 (0.9390)	Accuracy 64.453 (65.490)
Epoch: [11][ 830/1109]	Loss 0.8717 (0.9392)	Accuracy 67.969 (65.474)
Epoch: [11][ 840/1109]	Loss 0.9764 (0.9390)	Accuracy 66.016 (65.480)
Epoch: [11][ 850/1109]	Loss 0.8499 (0.9387)	Accuracy 67.969 (65.485)
Epoch: [11][ 860/1109]	Loss 0.8725 (0.9388)	Accuracy 66.406 (65.468)
Epoch: [11][ 870/1109]	Loss 1.0276 (0.9388)	Accuracy 60.547 (65.457)
Epoch: [11][ 880/1109]	Loss 0.9367 (0.9388)	Accuracy 66.797 (65.461)
Epoch: [11][ 890/1109]	Loss 0.9664 (0.9386)	Accuracy 67.188 (65.483)
Epoch: [11][ 900/1109]	Loss 0.9804 (0.9385)	Accuracy 66.797 (65.491)
Epoch: [11][ 910/1109]	Loss 0.9168 (0.9384)	Accuracy 67.188 (65.491)
Epoch: [11][ 920/1109]	Loss 0.9709 (0.9382)	Accuracy 64.062 (65.491)
Epoch: [11][ 930/1109]	Loss 0.9420 (0.9381)	Accuracy 65.234 (65.479)
Epoch: [11][ 940/1109]	Loss 0.9465 (0.9381)	Accuracy 64.844 (65.478)
Epoch: [11][ 950/1109]	Loss 0.8559 (0.9377)	Accuracy 70.312 (65.498)
Epoch: [11][ 960/1109]	Loss 0.9467 (0.9376)	Accuracy 65.625 (65.494)
Epoch: [11][ 970/1109]	Loss 0.9227 (0.9376)	Accuracy 64.844 (65.497)
Epoch: [11][ 980/1109]	Loss 0.9577 (0.9375)	Accuracy 60.547 (65.498)
Epoch: [11][ 990/1109]	Loss 0.8940 (0.9375)	Accuracy 66.016 (65.510)
Epoch: [11][1000/1109]	Loss 1.0918 (0.9379)	Accuracy 60.547 (65.493)
Epoch: [11][1010/1109]	Loss 1.0521 (0.9380)	Accuracy 58.594 (65.486)
Epoch: [11][1020/1109]	Loss 0.9814 (0.9379)	Accuracy 62.109 (65.493)
Epoch: [11][1030/1109]	Loss 0.8540 (0.9376)	Accuracy 72.656 (65.514)
Epoch: [11][1040/1109]	Loss 0.9970 (0.9375)	Accuracy 62.109 (65.518)
Epoch: [11][1050/1109]	Loss 1.0205 (0.9375)	Accuracy 60.938 (65.515)
Epoch: [11][1060/1109]	Loss 0.8746 (0.9374)	Accuracy 65.625 (65.522)
Epoch: [11][1070/1109]	Loss 0.8988 (0.9371)	Accuracy 66.016 (65.538)
Epoch: [11][1080/1109]	Loss 1.0058 (0.9372)	Accuracy 62.109 (65.537)
Epoch: [11][1090/1109]	Loss 0.9325 (0.9373)	Accuracy 65.234 (65.529)
Epoch: [11][1100/1109]	Loss 0.8558 (0.9371)	Accuracy 72.656 (65.537)
Test: [ 0/14]	Loss 0.9813 (0.9813)	Accuracy 64.062 (64.062)
Test: [10/14]	Loss 2.0404 (1.0587)	Accuracy 46.875 (64.098)
 * Accuracy 63.475
Current best accuracy: 64.53272247314453
803.3196773529053
Current learning rate: 0.00010000000000000002
Epoch: [12][   0/1109]	Loss 0.7863 (0.7863)	Accuracy 74.219 (74.219)
Epoch: [12][  10/1109]	Loss 0.9533 (0.9246)	Accuracy 67.969 (67.578)
Epoch: [12][  20/1109]	Loss 0.9864 (0.9248)	Accuracy 61.719 (66.853)
Epoch: [12][  30/1109]	Loss 1.0030 (0.9277)	Accuracy 60.938 (66.394)
Epoch: [12][  40/1109]	Loss 0.8483 (0.9299)	Accuracy 68.750 (66.216)
Epoch: [12][  50/1109]	Loss 0.9960 (0.9320)	Accuracy 60.547 (66.138)
Epoch: [12][  60/1109]	Loss 0.9403 (0.9294)	Accuracy 63.281 (66.169)
Epoch: [12][  70/1109]	Loss 1.0368 (0.9281)	Accuracy 60.547 (66.368)
Epoch: [12][  80/1109]	Loss 0.9441 (0.9262)	Accuracy 65.234 (66.488)
Epoch: [12][  90/1109]	Loss 1.0906 (0.9298)	Accuracy 59.375 (66.338)
Epoch: [12][ 100/1109]	Loss 0.8959 (0.9293)	Accuracy 71.875 (66.337)
Epoch: [12][ 110/1109]	Loss 0.9603 (0.9308)	Accuracy 66.797 (66.311)
Epoch: [12][ 120/1109]	Loss 0.8583 (0.9321)	Accuracy 67.969 (66.148)
Epoch: [12][ 130/1109]	Loss 0.9753 (0.9318)	Accuracy 63.672 (66.141)
Epoch: [12][ 140/1109]	Loss 0.8683 (0.9319)	Accuracy 68.750 (66.063)
Epoch: [12][ 150/1109]	Loss 0.9222 (0.9332)	Accuracy 66.016 (65.964)
Epoch: [12][ 160/1109]	Loss 0.9086 (0.9352)	Accuracy 65.625 (65.855)
Epoch: [12][ 170/1109]	Loss 0.9150 (0.9367)	Accuracy 66.016 (65.755)
Epoch: [12][ 180/1109]	Loss 0.9803 (0.9378)	Accuracy 62.500 (65.694)
Epoch: [12][ 190/1109]	Loss 0.8879 (0.9380)	Accuracy 63.672 (65.692)
Epoch: [12][ 200/1109]	Loss 1.0082 (0.9386)	Accuracy 66.016 (65.639)
Epoch: [12][ 210/1109]	Loss 0.8246 (0.9373)	Accuracy 68.750 (65.714)
Epoch: [12][ 220/1109]	Loss 1.0707 (0.9378)	Accuracy 58.594 (65.719)
Epoch: [12][ 230/1109]	Loss 0.8528 (0.9360)	Accuracy 67.188 (65.760)
Epoch: [12][ 240/1109]	Loss 0.8887 (0.9354)	Accuracy 68.359 (65.764)
Epoch: [12][ 250/1109]	Loss 0.9117 (0.9351)	Accuracy 69.922 (65.795)
Epoch: [12][ 260/1109]	Loss 0.9276 (0.9362)	Accuracy 62.891 (65.712)
Epoch: [12][ 270/1109]	Loss 0.8072 (0.9359)	Accuracy 71.484 (65.714)
Epoch: [12][ 280/1109]	Loss 1.0326 (0.9366)	Accuracy 61.719 (65.686)
Epoch: [12][ 290/1109]	Loss 0.9330 (0.9381)	Accuracy 62.500 (65.602)
Epoch: [12][ 300/1109]	Loss 0.8691 (0.9374)	Accuracy 68.359 (65.633)
Epoch: [12][ 310/1109]	Loss 1.0495 (0.9370)	Accuracy 63.672 (65.630)
Epoch: [12][ 320/1109]	Loss 0.9544 (0.9377)	Accuracy 65.625 (65.607)
Epoch: [12][ 330/1109]	Loss 0.9104 (0.9379)	Accuracy 64.062 (65.564)
Epoch: [12][ 340/1109]	Loss 1.0372 (0.9370)	Accuracy 59.375 (65.553)
Epoch: [12][ 350/1109]	Loss 0.9900 (0.9384)	Accuracy 64.062 (65.508)
Epoch: [12][ 360/1109]	Loss 0.9438 (0.9390)	Accuracy 66.016 (65.467)
Epoch: [12][ 370/1109]	Loss 0.8501 (0.9379)	Accuracy 71.484 (65.508)
Epoch: [12][ 380/1109]	Loss 0.9620 (0.9381)	Accuracy 63.281 (65.502)
Epoch: [12][ 390/1109]	Loss 0.9155 (0.9371)	Accuracy 64.062 (65.534)
Epoch: [12][ 400/1109]	Loss 0.9021 (0.9377)	Accuracy 64.844 (65.524)
Epoch: [12][ 410/1109]	Loss 0.9251 (0.9378)	Accuracy 66.016 (65.539)
Epoch: [12][ 420/1109]	Loss 0.9781 (0.9371)	Accuracy 62.109 (65.551)
Epoch: [12][ 430/1109]	Loss 0.9489 (0.9365)	Accuracy 65.625 (65.594)
Epoch: [12][ 440/1109]	Loss 0.8465 (0.9356)	Accuracy 67.969 (65.643)
Epoch: [12][ 450/1109]	Loss 0.9526 (0.9356)	Accuracy 60.547 (65.631)
Epoch: [12][ 460/1109]	Loss 0.9214 (0.9355)	Accuracy 65.625 (65.632)
Epoch: [12][ 470/1109]	Loss 0.9428 (0.9359)	Accuracy 68.750 (65.618)
Epoch: [12][ 480/1109]	Loss 0.9365 (0.9362)	Accuracy 66.016 (65.606)
Epoch: [12][ 490/1109]	Loss 1.0442 (0.9365)	Accuracy 62.500 (65.590)
Epoch: [12][ 500/1109]	Loss 0.9308 (0.9364)	Accuracy 66.406 (65.585)
Epoch: [12][ 510/1109]	Loss 0.8963 (0.9365)	Accuracy 66.406 (65.571)
Epoch: [12][ 520/1109]	Loss 0.9095 (0.9363)	Accuracy 66.406 (65.582)
Epoch: [12][ 530/1109]	Loss 0.9849 (0.9363)	Accuracy 63.281 (65.587)
Epoch: [12][ 540/1109]	Loss 0.9465 (0.9360)	Accuracy 61.328 (65.590)
Epoch: [12][ 550/1109]	Loss 0.9464 (0.9371)	Accuracy 67.188 (65.560)
Epoch: [12][ 560/1109]	Loss 0.8617 (0.9364)	Accuracy 70.703 (65.596)
Epoch: [12][ 570/1109]	Loss 0.8926 (0.9362)	Accuracy 69.922 (65.609)
Epoch: [12][ 580/1109]	Loss 0.9614 (0.9365)	Accuracy 63.672 (65.593)
Epoch: [12][ 590/1109]	Loss 1.0394 (0.9367)	Accuracy 60.156 (65.597)
Epoch: [12][ 600/1109]	Loss 0.8877 (0.9365)	Accuracy 67.578 (65.598)
Epoch: [12][ 610/1109]	Loss 0.9005 (0.9364)	Accuracy 68.359 (65.603)
Epoch: [12][ 620/1109]	Loss 0.8660 (0.9362)	Accuracy 67.188 (65.612)
Epoch: [12][ 630/1109]	Loss 0.9391 (0.9364)	Accuracy 62.891 (65.583)
Epoch: [12][ 640/1109]	Loss 0.9069 (0.9363)	Accuracy 66.406 (65.590)
Epoch: [12][ 650/1109]	Loss 0.8784 (0.9362)	Accuracy 67.578 (65.599)
Epoch: [12][ 660/1109]	Loss 0.9651 (0.9363)	Accuracy 67.188 (65.599)
Epoch: [12][ 670/1109]	Loss 0.8462 (0.9359)	Accuracy 67.969 (65.606)
Epoch: [12][ 680/1109]	Loss 0.8881 (0.9350)	Accuracy 67.188 (65.634)
Epoch: [12][ 690/1109]	Loss 1.0138 (0.9352)	Accuracy 63.281 (65.639)
Epoch: [12][ 700/1109]	Loss 1.0373 (0.9351)	Accuracy 60.547 (65.634)
Epoch: [12][ 710/1109]	Loss 1.0262 (0.9357)	Accuracy 62.500 (65.603)
Epoch: [12][ 720/1109]	Loss 0.9911 (0.9357)	Accuracy 66.406 (65.611)
Epoch: [12][ 730/1109]	Loss 0.9256 (0.9357)	Accuracy 65.625 (65.598)
Epoch: [12][ 740/1109]	Loss 0.9131 (0.9355)	Accuracy 69.531 (65.604)
Epoch: [12][ 750/1109]	Loss 1.0142 (0.9354)	Accuracy 62.891 (65.600)
Epoch: [12][ 760/1109]	Loss 0.9236 (0.9355)	Accuracy 63.281 (65.588)
Epoch: [12][ 770/1109]	Loss 0.9308 (0.9357)	Accuracy 63.672 (65.580)
Epoch: [12][ 780/1109]	Loss 0.9426 (0.9355)	Accuracy 64.062 (65.585)
Epoch: [12][ 790/1109]	Loss 0.8980 (0.9357)	Accuracy 68.750 (65.589)
Epoch: [12][ 800/1109]	Loss 0.9704 (0.9357)	Accuracy 64.453 (65.570)
Epoch: [12][ 810/1109]	Loss 0.9620 (0.9357)	Accuracy 64.453 (65.566)
Epoch: [12][ 820/1109]	Loss 1.0090 (0.9357)	Accuracy 66.016 (65.584)
Epoch: [12][ 830/1109]	Loss 0.9576 (0.9354)	Accuracy 66.016 (65.588)
Epoch: [12][ 840/1109]	Loss 0.9532 (0.9355)	Accuracy 62.109 (65.572)
Epoch: [12][ 850/1109]	Loss 0.9056 (0.9352)	Accuracy 66.016 (65.589)
Epoch: [12][ 860/1109]	Loss 1.0113 (0.9355)	Accuracy 62.891 (65.594)
Epoch: [12][ 870/1109]	Loss 0.9423 (0.9353)	Accuracy 63.281 (65.599)
Epoch: [12][ 880/1109]	Loss 0.8670 (0.9351)	Accuracy 66.797 (65.606)
Epoch: [12][ 890/1109]	Loss 0.9042 (0.9353)	Accuracy 71.484 (65.606)
Epoch: [12][ 900/1109]	Loss 0.9543 (0.9352)	Accuracy 64.453 (65.609)
Epoch: [12][ 910/1109]	Loss 0.8245 (0.9354)	Accuracy 69.141 (65.609)
Epoch: [12][ 920/1109]	Loss 1.1163 (0.9358)	Accuracy 58.203 (65.591)
Epoch: [12][ 930/1109]	Loss 0.9313 (0.9357)	Accuracy 64.844 (65.601)
Epoch: [12][ 940/1109]	Loss 0.8039 (0.9357)	Accuracy 73.047 (65.600)
Epoch: [12][ 950/1109]	Loss 0.9095 (0.9356)	Accuracy 64.062 (65.599)
Epoch: [12][ 960/1109]	Loss 0.8788 (0.9354)	Accuracy 68.750 (65.605)
Epoch: [12][ 970/1109]	Loss 0.8469 (0.9352)	Accuracy 66.797 (65.617)
Epoch: [12][ 980/1109]	Loss 0.8969 (0.9353)	Accuracy 64.844 (65.616)
Epoch: [12][ 990/1109]	Loss 0.9536 (0.9349)	Accuracy 63.672 (65.630)
Epoch: [12][1000/1109]	Loss 0.8406 (0.9346)	Accuracy 67.969 (65.636)
Epoch: [12][1010/1109]	Loss 0.9647 (0.9346)	Accuracy 65.234 (65.639)
Epoch: [12][1020/1109]	Loss 1.0208 (0.9344)	Accuracy 58.984 (65.638)
Epoch: [12][1030/1109]	Loss 0.9108 (0.9343)	Accuracy 67.578 (65.638)
Epoch: [12][1040/1109]	Loss 1.0220 (0.9346)	Accuracy 65.234 (65.636)
Epoch: [12][1050/1109]	Loss 0.9965 (0.9343)	Accuracy 61.328 (65.645)
Epoch: [12][1060/1109]	Loss 1.0224 (0.9344)	Accuracy 63.672 (65.645)
Epoch: [12][1070/1109]	Loss 0.9591 (0.9342)	Accuracy 65.234 (65.655)
Epoch: [12][1080/1109]	Loss 1.0141 (0.9343)	Accuracy 62.109 (65.650)
Epoch: [12][1090/1109]	Loss 1.0065 (0.9341)	Accuracy 62.109 (65.655)
Epoch: [12][1100/1109]	Loss 0.9726 (0.9342)	Accuracy 64.062 (65.655)
Test: [ 0/14]	Loss 0.9923 (0.9923)	Accuracy 64.453 (64.453)
Test: [10/14]	Loss 2.0262 (1.0466)	Accuracy 46.875 (64.737)
 * Accuracy 63.847
Current best accuracy: 64.53272247314453
924.1562054157257
Current learning rate: 0.00010000000000000002
Epoch: [13][   0/1109]	Loss 0.9157 (0.9157)	Accuracy 67.188 (67.188)
Epoch: [13][  10/1109]	Loss 1.0395 (0.9581)	Accuracy 62.891 (64.915)
Epoch: [13][  20/1109]	Loss 0.8344 (0.9310)	Accuracy 66.797 (66.034)
Epoch: [13][  30/1109]	Loss 1.0109 (0.9380)	Accuracy 63.281 (65.953)
Epoch: [13][  40/1109]	Loss 0.9980 (0.9396)	Accuracy 65.234 (65.939)
Epoch: [13][  50/1109]	Loss 0.8572 (0.9308)	Accuracy 68.359 (66.100)
Epoch: [13][  60/1109]	Loss 0.9446 (0.9332)	Accuracy 64.062 (65.759)
Epoch: [13][  70/1109]	Loss 0.9362 (0.9338)	Accuracy 64.453 (65.669)
Epoch: [13][  80/1109]	Loss 1.0243 (0.9324)	Accuracy 62.891 (65.693)
Epoch: [13][  90/1109]	Loss 0.8989 (0.9307)	Accuracy 64.844 (65.762)
Epoch: [13][ 100/1109]	Loss 0.9497 (0.9308)	Accuracy 62.500 (65.784)
Epoch: [13][ 110/1109]	Loss 0.9531 (0.9315)	Accuracy 63.281 (65.724)
Epoch: [13][ 120/1109]	Loss 0.9342 (0.9322)	Accuracy 66.406 (65.635)
Epoch: [13][ 130/1109]	Loss 0.9442 (0.9309)	Accuracy 68.750 (65.747)
Epoch: [13][ 140/1109]	Loss 0.9274 (0.9312)	Accuracy 66.797 (65.669)
Epoch: [13][ 150/1109]	Loss 0.9775 (0.9316)	Accuracy 63.672 (65.685)
Epoch: [13][ 160/1109]	Loss 1.0211 (0.9328)	Accuracy 64.453 (65.703)
Epoch: [13][ 170/1109]	Loss 0.9241 (0.9326)	Accuracy 65.234 (65.700)
Epoch: [13][ 180/1109]	Loss 1.0126 (0.9335)	Accuracy 60.547 (65.616)
Epoch: [13][ 190/1109]	Loss 0.9718 (0.9329)	Accuracy 62.891 (65.598)
Epoch: [13][ 200/1109]	Loss 0.9624 (0.9318)	Accuracy 63.672 (65.687)
Epoch: [13][ 210/1109]	Loss 0.9465 (0.9315)	Accuracy 63.281 (65.708)
Epoch: [13][ 220/1109]	Loss 0.9667 (0.9311)	Accuracy 66.016 (65.724)
Epoch: [13][ 230/1109]	Loss 1.0172 (0.9315)	Accuracy 66.016 (65.789)
Epoch: [13][ 240/1109]	Loss 0.9730 (0.9326)	Accuracy 64.844 (65.745)
Epoch: [13][ 250/1109]	Loss 0.9900 (0.9330)	Accuracy 66.406 (65.779)
Epoch: [13][ 260/1109]	Loss 0.8248 (0.9318)	Accuracy 71.484 (65.820)
Epoch: [13][ 270/1109]	Loss 1.0063 (0.9331)	Accuracy 63.281 (65.789)
Epoch: [13][ 280/1109]	Loss 0.9581 (0.9339)	Accuracy 64.062 (65.758)
Epoch: [13][ 290/1109]	Loss 1.0130 (0.9352)	Accuracy 62.891 (65.707)
Epoch: [13][ 300/1109]	Loss 0.9739 (0.9361)	Accuracy 63.281 (65.663)
Epoch: [13][ 310/1109]	Loss 0.9197 (0.9365)	Accuracy 68.750 (65.692)
Epoch: [13][ 320/1109]	Loss 0.8826 (0.9359)	Accuracy 64.453 (65.675)
Epoch: [13][ 330/1109]	Loss 0.9403 (0.9360)	Accuracy 64.453 (65.676)
Epoch: [13][ 340/1109]	Loss 0.8776 (0.9359)	Accuracy 64.453 (65.655)
Epoch: [13][ 350/1109]	Loss 0.9804 (0.9356)	Accuracy 64.062 (65.639)
Epoch: [13][ 360/1109]	Loss 0.9231 (0.9366)	Accuracy 64.062 (65.576)
Epoch: [13][ 370/1109]	Loss 0.9231 (0.9360)	Accuracy 65.234 (65.602)
Epoch: [13][ 380/1109]	Loss 0.9006 (0.9356)	Accuracy 69.922 (65.619)
Epoch: [13][ 390/1109]	Loss 1.0173 (0.9362)	Accuracy 60.547 (65.588)
Epoch: [13][ 400/1109]	Loss 0.7966 (0.9358)	Accuracy 70.312 (65.628)
Epoch: [13][ 410/1109]	Loss 0.9062 (0.9363)	Accuracy 64.062 (65.616)
Epoch: [13][ 420/1109]	Loss 0.9144 (0.9369)	Accuracy 64.453 (65.593)
Epoch: [13][ 430/1109]	Loss 0.9489 (0.9371)	Accuracy 65.234 (65.601)
Epoch: [13][ 440/1109]	Loss 0.9936 (0.9369)	Accuracy 62.500 (65.617)
Epoch: [13][ 450/1109]	Loss 0.9160 (0.9365)	Accuracy 66.797 (65.613)
Epoch: [13][ 460/1109]	Loss 1.0005 (0.9365)	Accuracy 60.547 (65.609)
Epoch: [13][ 470/1109]	Loss 0.9790 (0.9363)	Accuracy 62.891 (65.618)
Epoch: [13][ 480/1109]	Loss 0.9383 (0.9367)	Accuracy 64.453 (65.608)
Epoch: [13][ 490/1109]	Loss 0.9387 (0.9369)	Accuracy 67.188 (65.594)
Epoch: [13][ 500/1109]	Loss 1.0681 (0.9367)	Accuracy 61.719 (65.611)
Epoch: [13][ 510/1109]	Loss 0.8733 (0.9375)	Accuracy 70.312 (65.584)
Epoch: [13][ 520/1109]	Loss 0.9736 (0.9377)	Accuracy 64.453 (65.572)
Epoch: [13][ 530/1109]	Loss 0.8500 (0.9376)	Accuracy 69.531 (65.571)
Epoch: [13][ 540/1109]	Loss 0.9372 (0.9371)	Accuracy 65.234 (65.577)
Epoch: [13][ 550/1109]	Loss 0.9563 (0.9377)	Accuracy 65.625 (65.560)
Epoch: [13][ 560/1109]	Loss 0.8931 (0.9375)	Accuracy 66.797 (65.554)
Epoch: [13][ 570/1109]	Loss 0.9831 (0.9376)	Accuracy 64.062 (65.567)
Epoch: [13][ 580/1109]	Loss 0.9370 (0.9382)	Accuracy 63.672 (65.536)
Epoch: [13][ 590/1109]	Loss 0.9938 (0.9381)	Accuracy 64.453 (65.544)
Epoch: [13][ 600/1109]	Loss 0.9532 (0.9383)	Accuracy 62.891 (65.524)
Epoch: [13][ 610/1109]	Loss 0.9714 (0.9382)	Accuracy 64.844 (65.537)
Epoch: [13][ 620/1109]	Loss 0.8786 (0.9376)	Accuracy 72.656 (65.561)
Epoch: [13][ 630/1109]	Loss 0.8958 (0.9376)	Accuracy 65.234 (65.559)
Epoch: [13][ 640/1109]	Loss 0.9322 (0.9376)	Accuracy 65.234 (65.550)
Epoch: [13][ 650/1109]	Loss 0.8788 (0.9378)	Accuracy 67.969 (65.539)
Epoch: [13][ 660/1109]	Loss 0.9156 (0.9378)	Accuracy 65.234 (65.529)
Epoch: [13][ 670/1109]	Loss 0.9427 (0.9378)	Accuracy 67.188 (65.539)
Epoch: [13][ 680/1109]	Loss 0.9760 (0.9378)	Accuracy 65.234 (65.546)
Epoch: [13][ 690/1109]	Loss 0.8121 (0.9378)	Accuracy 69.922 (65.544)
Epoch: [13][ 700/1109]	Loss 0.9091 (0.9378)	Accuracy 67.969 (65.550)
Epoch: [13][ 710/1109]	Loss 0.9765 (0.9379)	Accuracy 65.625 (65.533)
Epoch: [13][ 720/1109]	Loss 1.0399 (0.9379)	Accuracy 60.938 (65.542)
Epoch: [13][ 730/1109]	Loss 0.8797 (0.9380)	Accuracy 67.969 (65.542)
Epoch: [13][ 740/1109]	Loss 0.7821 (0.9378)	Accuracy 72.656 (65.546)
Epoch: [13][ 750/1109]	Loss 0.9297 (0.9378)	Accuracy 64.844 (65.546)
Epoch: [13][ 760/1109]	Loss 0.9554 (0.9377)	Accuracy 60.547 (65.540)
Epoch: [13][ 770/1109]	Loss 0.9585 (0.9378)	Accuracy 63.281 (65.529)
Epoch: [13][ 780/1109]	Loss 0.9051 (0.9379)	Accuracy 64.453 (65.530)
Epoch: [13][ 790/1109]	Loss 0.9705 (0.9384)	Accuracy 60.938 (65.500)
Epoch: [13][ 800/1109]	Loss 0.9625 (0.9384)	Accuracy 62.891 (65.498)
Epoch: [13][ 810/1109]	Loss 0.9483 (0.9385)	Accuracy 63.281 (65.494)
Epoch: [13][ 820/1109]	Loss 0.9464 (0.9384)	Accuracy 64.453 (65.512)
Epoch: [13][ 830/1109]	Loss 0.8409 (0.9380)	Accuracy 69.922 (65.524)
Epoch: [13][ 840/1109]	Loss 0.9511 (0.9383)	Accuracy 64.062 (65.503)
Epoch: [13][ 850/1109]	Loss 0.8450 (0.9381)	Accuracy 70.312 (65.526)
Epoch: [13][ 860/1109]	Loss 1.0060 (0.9385)	Accuracy 63.281 (65.508)
Epoch: [13][ 870/1109]	Loss 0.9173 (0.9384)	Accuracy 63.281 (65.511)
Epoch: [13][ 880/1109]	Loss 0.9856 (0.9383)	Accuracy 66.016 (65.500)
Epoch: [13][ 890/1109]	Loss 0.8750 (0.9384)	Accuracy 69.922 (65.496)
Epoch: [13][ 900/1109]	Loss 1.0867 (0.9388)	Accuracy 58.594 (65.473)
Epoch: [13][ 910/1109]	Loss 1.0812 (0.9387)	Accuracy 60.156 (65.479)
Epoch: [13][ 920/1109]	Loss 0.8994 (0.9390)	Accuracy 64.062 (65.466)
Epoch: [13][ 930/1109]	Loss 0.8282 (0.9387)	Accuracy 70.312 (65.479)
Epoch: [13][ 940/1109]	Loss 0.9104 (0.9388)	Accuracy 69.531 (65.474)
Epoch: [13][ 950/1109]	Loss 0.9481 (0.9387)	Accuracy 62.500 (65.475)
Epoch: [13][ 960/1109]	Loss 0.7966 (0.9384)	Accuracy 73.828 (65.488)
Epoch: [13][ 970/1109]	Loss 0.9988 (0.9383)	Accuracy 60.547 (65.480)
Epoch: [13][ 980/1109]	Loss 0.9605 (0.9382)	Accuracy 62.891 (65.487)
Epoch: [13][ 990/1109]	Loss 0.9397 (0.9383)	Accuracy 68.750 (65.487)
Epoch: [13][1000/1109]	Loss 1.0002 (0.9386)	Accuracy 62.109 (65.471)
Epoch: [13][1010/1109]	Loss 0.8709 (0.9386)	Accuracy 66.797 (65.469)
Epoch: [13][1020/1109]	Loss 0.9594 (0.9384)	Accuracy 65.234 (65.482)
Epoch: [13][1030/1109]	Loss 0.9515 (0.9383)	Accuracy 65.234 (65.475)
Epoch: [13][1040/1109]	Loss 0.8163 (0.9381)	Accuracy 69.922 (65.482)
Epoch: [13][1050/1109]	Loss 1.0559 (0.9382)	Accuracy 59.375 (65.474)
Epoch: [13][1060/1109]	Loss 0.9085 (0.9380)	Accuracy 67.578 (65.490)
Epoch: [13][1070/1109]	Loss 0.8253 (0.9379)	Accuracy 71.484 (65.497)
Epoch: [13][1080/1109]	Loss 0.9059 (0.9379)	Accuracy 67.969 (65.500)
Epoch: [13][1090/1109]	Loss 0.9213 (0.9380)	Accuracy 66.797 (65.498)
Epoch: [13][1100/1109]	Loss 0.9352 (0.9376)	Accuracy 69.141 (65.510)
Test: [ 0/14]	Loss 0.9730 (0.9730)	Accuracy 64.453 (64.453)
Test: [10/14]	Loss 2.0302 (1.0532)	Accuracy 47.266 (64.240)
 * Accuracy 63.590
Current best accuracy: 64.53272247314453
956.9671146869659
Current learning rate: 0.00010000000000000002
Epoch: [14][   0/1109]	Loss 1.1469 (1.1469)	Accuracy 54.297 (54.297)
Epoch: [14][  10/1109]	Loss 0.9242 (0.9573)	Accuracy 67.578 (64.205)
Epoch: [14][  20/1109]	Loss 0.8911 (0.9502)	Accuracy 64.453 (64.342)
Epoch: [14][  30/1109]	Loss 0.9260 (0.9469)	Accuracy 64.844 (64.516)
Epoch: [14][  40/1109]	Loss 0.9862 (0.9391)	Accuracy 64.453 (64.891)
Epoch: [14][  50/1109]	Loss 0.7898 (0.9331)	Accuracy 70.703 (65.564)
Epoch: [14][  60/1109]	Loss 0.8943 (0.9274)	Accuracy 64.844 (65.888)
Epoch: [14][  70/1109]	Loss 0.9526 (0.9260)	Accuracy 65.234 (66.010)
Epoch: [14][  80/1109]	Loss 0.9167 (0.9275)	Accuracy 67.969 (65.982)
Epoch: [14][  90/1109]	Loss 0.8902 (0.9254)	Accuracy 67.188 (66.132)
Epoch: [14][ 100/1109]	Loss 0.7738 (0.9259)	Accuracy 70.312 (66.000)
Epoch: [14][ 110/1109]	Loss 0.9155 (0.9276)	Accuracy 68.750 (65.984)
Epoch: [14][ 120/1109]	Loss 1.0271 (0.9291)	Accuracy 62.500 (65.896)
Epoch: [14][ 130/1109]	Loss 0.8704 (0.9297)	Accuracy 67.969 (65.941)
Epoch: [14][ 140/1109]	Loss 0.9976 (0.9303)	Accuracy 63.672 (66.013)
Epoch: [14][ 150/1109]	Loss 0.9528 (0.9296)	Accuracy 65.234 (66.034)
Epoch: [14][ 160/1109]	Loss 1.0454 (0.9315)	Accuracy 60.938 (65.938)
Epoch: [14][ 170/1109]	Loss 0.9738 (0.9335)	Accuracy 63.281 (65.856)
Epoch: [14][ 180/1109]	Loss 0.8974 (0.9327)	Accuracy 68.359 (65.880)
Epoch: [14][ 190/1109]	Loss 0.9237 (0.9321)	Accuracy 70.312 (65.938)
Epoch: [14][ 200/1109]	Loss 0.9308 (0.9327)	Accuracy 67.578 (65.940)
Epoch: [14][ 210/1109]	Loss 0.9518 (0.9329)	Accuracy 66.016 (65.877)
Epoch: [14][ 220/1109]	Loss 0.9103 (0.9342)	Accuracy 67.969 (65.849)
Epoch: [14][ 230/1109]	Loss 0.9792 (0.9350)	Accuracy 68.750 (65.823)
Epoch: [14][ 240/1109]	Loss 0.9636 (0.9338)	Accuracy 66.016 (65.888)
Epoch: [14][ 250/1109]	Loss 0.9479 (0.9332)	Accuracy 64.453 (65.888)
Epoch: [14][ 260/1109]	Loss 0.8939 (0.9334)	Accuracy 67.578 (65.848)
Epoch: [14][ 270/1109]	Loss 0.9274 (0.9339)	Accuracy 63.672 (65.825)
Epoch: [14][ 280/1109]	Loss 0.9358 (0.9338)	Accuracy 68.750 (65.828)
Epoch: [14][ 290/1109]	Loss 1.0501 (0.9336)	Accuracy 60.156 (65.821)
Epoch: [14][ 300/1109]	Loss 0.9236 (0.9338)	Accuracy 67.188 (65.804)
Epoch: [14][ 310/1109]	Loss 0.8736 (0.9341)	Accuracy 69.922 (65.788)
Epoch: [14][ 320/1109]	Loss 0.9332 (0.9340)	Accuracy 62.891 (65.781)
Epoch: [14][ 330/1109]	Loss 0.9449 (0.9333)	Accuracy 66.016 (65.814)
Epoch: [14][ 340/1109]	Loss 1.0124 (0.9333)	Accuracy 62.109 (65.821)
Epoch: [14][ 350/1109]	Loss 0.8892 (0.9325)	Accuracy 65.625 (65.869)
Epoch: [14][ 360/1109]	Loss 0.9818 (0.9321)	Accuracy 65.234 (65.915)
Epoch: [14][ 370/1109]	Loss 0.9071 (0.9315)	Accuracy 69.531 (65.927)
Epoch: [14][ 380/1109]	Loss 0.9124 (0.9315)	Accuracy 64.453 (65.910)
Epoch: [14][ 390/1109]	Loss 0.9091 (0.9316)	Accuracy 66.016 (65.901)
Epoch: [14][ 400/1109]	Loss 0.8418 (0.9316)	Accuracy 69.922 (65.886)
Epoch: [14][ 410/1109]	Loss 0.9725 (0.9317)	Accuracy 63.672 (65.844)
Epoch: [14][ 420/1109]	Loss 0.8783 (0.9315)	Accuracy 66.016 (65.838)
Epoch: [14][ 430/1109]	Loss 0.9843 (0.9318)	Accuracy 63.672 (65.832)
Epoch: [14][ 440/1109]	Loss 0.9959 (0.9322)	Accuracy 65.234 (65.823)
Epoch: [14][ 450/1109]	Loss 0.9246 (0.9324)	Accuracy 64.453 (65.835)
Epoch: [14][ 460/1109]	Loss 1.0271 (0.9319)	Accuracy 60.938 (65.846)
Epoch: [14][ 470/1109]	Loss 0.8848 (0.9320)	Accuracy 67.969 (65.823)
Epoch: [14][ 480/1109]	Loss 0.8861 (0.9317)	Accuracy 66.406 (65.827)
Epoch: [14][ 490/1109]	Loss 0.9253 (0.9322)	Accuracy 62.500 (65.811)
Epoch: [14][ 500/1109]	Loss 0.9152 (0.9324)	Accuracy 68.359 (65.783)
Epoch: [14][ 510/1109]	Loss 1.0163 (0.9327)	Accuracy 64.844 (65.774)
Epoch: [14][ 520/1109]	Loss 1.1180 (0.9334)	Accuracy 60.547 (65.740)
Epoch: [14][ 530/1109]	Loss 1.0483 (0.9330)	Accuracy 61.328 (65.740)
Epoch: [14][ 540/1109]	Loss 1.0032 (0.9330)	Accuracy 61.328 (65.745)
Epoch: [14][ 550/1109]	Loss 0.9532 (0.9332)	Accuracy 65.234 (65.741)
Epoch: [14][ 560/1109]	Loss 0.8978 (0.9333)	Accuracy 67.578 (65.743)
Epoch: [14][ 570/1109]	Loss 0.8393 (0.9333)	Accuracy 70.703 (65.739)
Epoch: [14][ 580/1109]	Loss 0.9650 (0.9335)	Accuracy 61.719 (65.727)
Epoch: [14][ 590/1109]	Loss 0.8424 (0.9337)	Accuracy 69.922 (65.715)
Epoch: [14][ 600/1109]	Loss 0.8791 (0.9337)	Accuracy 68.750 (65.713)
Epoch: [14][ 610/1109]	Loss 0.9491 (0.9341)	Accuracy 66.797 (65.712)
Epoch: [14][ 620/1109]	Loss 0.8868 (0.9338)	Accuracy 68.750 (65.718)
Epoch: [14][ 630/1109]	Loss 0.9572 (0.9334)	Accuracy 63.672 (65.728)
Epoch: [14][ 640/1109]	Loss 0.9385 (0.9336)	Accuracy 66.406 (65.716)
Epoch: [14][ 650/1109]	Loss 1.0161 (0.9333)	Accuracy 64.062 (65.738)
Epoch: [14][ 660/1109]	Loss 0.9232 (0.9331)	Accuracy 68.750 (65.752)
Epoch: [14][ 670/1109]	Loss 0.8802 (0.9333)	Accuracy 65.234 (65.743)
Epoch: [14][ 680/1109]	Loss 0.9291 (0.9334)	Accuracy 65.234 (65.748)
Epoch: [14][ 690/1109]	Loss 0.9573 (0.9333)	Accuracy 63.281 (65.765)
Epoch: [14][ 700/1109]	Loss 0.9860 (0.9330)	Accuracy 65.625 (65.792)
Epoch: [14][ 710/1109]	Loss 0.8134 (0.9329)	Accuracy 74.219 (65.816)
Epoch: [14][ 720/1109]	Loss 0.9923 (0.9330)	Accuracy 63.672 (65.809)
Epoch: [14][ 730/1109]	Loss 0.9389 (0.9327)	Accuracy 68.750 (65.818)
Epoch: [14][ 740/1109]	Loss 0.8167 (0.9322)	Accuracy 70.312 (65.835)
Epoch: [14][ 750/1109]	Loss 1.0228 (0.9325)	Accuracy 62.109 (65.822)
Epoch: [14][ 760/1109]	Loss 0.9881 (0.9327)	Accuracy 64.062 (65.810)
Epoch: [14][ 770/1109]	Loss 0.9177 (0.9324)	Accuracy 70.312 (65.826)
Epoch: [14][ 780/1109]	Loss 0.9579 (0.9325)	Accuracy 65.625 (65.826)
Epoch: [14][ 790/1109]	Loss 0.9340 (0.9329)	Accuracy 67.188 (65.814)
Epoch: [14][ 800/1109]	Loss 1.0348 (0.9333)	Accuracy 60.547 (65.802)
Epoch: [14][ 810/1109]	Loss 0.9834 (0.9339)	Accuracy 61.719 (65.773)
Epoch: [14][ 820/1109]	Loss 0.9054 (0.9337)	Accuracy 65.234 (65.764)
Epoch: [14][ 830/1109]	Loss 0.8289 (0.9333)	Accuracy 71.094 (65.788)
Epoch: [14][ 840/1109]	Loss 0.9531 (0.9333)	Accuracy 64.062 (65.783)
Epoch: [14][ 850/1109]	Loss 0.9235 (0.9335)	Accuracy 65.234 (65.765)
Epoch: [14][ 860/1109]	Loss 1.0404 (0.9335)	Accuracy 64.062 (65.765)
Epoch: [14][ 870/1109]	Loss 0.9402 (0.9336)	Accuracy 66.016 (65.763)
Epoch: [14][ 880/1109]	Loss 0.9619 (0.9338)	Accuracy 62.109 (65.753)
Epoch: [14][ 890/1109]	Loss 0.7733 (0.9332)	Accuracy 71.875 (65.768)
Epoch: [14][ 900/1109]	Loss 0.9965 (0.9331)	Accuracy 60.547 (65.769)
Epoch: [14][ 910/1109]	Loss 0.8689 (0.9329)	Accuracy 72.656 (65.778)
Epoch: [14][ 920/1109]	Loss 0.8783 (0.9325)	Accuracy 67.578 (65.800)
Epoch: [14][ 930/1109]	Loss 0.9444 (0.9327)	Accuracy 65.234 (65.795)
Epoch: [14][ 940/1109]	Loss 0.9893 (0.9328)	Accuracy 63.672 (65.786)
Epoch: [14][ 950/1109]	Loss 1.0458 (0.9329)	Accuracy 60.156 (65.778)
Epoch: [14][ 960/1109]	Loss 0.9831 (0.9331)	Accuracy 61.719 (65.764)
Epoch: [14][ 970/1109]	Loss 0.9557 (0.9332)	Accuracy 63.281 (65.748)
Epoch: [14][ 980/1109]	Loss 0.9479 (0.9333)	Accuracy 65.234 (65.758)
Epoch: [14][ 990/1109]	Loss 0.9933 (0.9333)	Accuracy 61.719 (65.750)
Epoch: [14][1000/1109]	Loss 0.8986 (0.9331)	Accuracy 65.625 (65.762)
Epoch: [14][1010/1109]	Loss 0.8455 (0.9328)	Accuracy 71.094 (65.775)
Epoch: [14][1020/1109]	Loss 0.8963 (0.9327)	Accuracy 67.578 (65.789)
Epoch: [14][1030/1109]	Loss 0.8832 (0.9325)	Accuracy 66.797 (65.800)
Epoch: [14][1040/1109]	Loss 0.8629 (0.9325)	Accuracy 66.406 (65.791)
Epoch: [14][1050/1109]	Loss 0.8832 (0.9328)	Accuracy 68.359 (65.780)
Epoch: [14][1060/1109]	Loss 1.0040 (0.9327)	Accuracy 62.500 (65.773)
Epoch: [14][1070/1109]	Loss 0.9511 (0.9326)	Accuracy 68.359 (65.780)
Epoch: [14][1080/1109]	Loss 0.8928 (0.9326)	Accuracy 66.797 (65.783)
Epoch: [14][1090/1109]	Loss 0.9138 (0.9324)	Accuracy 66.406 (65.787)
Epoch: [14][1100/1109]	Loss 0.9859 (0.9322)	Accuracy 64.844 (65.801)
Test: [ 0/14]	Loss 0.9512 (0.9512)	Accuracy 65.234 (65.234)
Test: [10/14]	Loss 2.0736 (1.0481)	Accuracy 46.875 (64.879)
 * Accuracy 63.818
Current best accuracy: 64.53272247314453
954.5459079742432
Current learning rate: 1.0000000000000003e-05
Epoch: [15][   0/1109]	Loss 1.1084 (1.1084)	Accuracy 62.109 (62.109)
Epoch: [15][  10/1109]	Loss 0.9241 (0.9562)	Accuracy 67.969 (64.453)
Epoch: [15][  20/1109]	Loss 0.9628 (0.9590)	Accuracy 63.281 (64.676)
Epoch: [15][  30/1109]	Loss 0.8405 (0.9592)	Accuracy 72.656 (64.831)
Epoch: [15][  40/1109]	Loss 0.8974 (0.9514)	Accuracy 69.141 (65.082)
Epoch: [15][  50/1109]	Loss 0.8967 (0.9485)	Accuracy 67.188 (65.043)
Epoch: [15][  60/1109]	Loss 0.8560 (0.9416)	Accuracy 67.969 (65.337)
Epoch: [15][  70/1109]	Loss 0.9741 (0.9407)	Accuracy 65.234 (65.350)
Epoch: [15][  80/1109]	Loss 0.9763 (0.9413)	Accuracy 65.234 (65.432)
Epoch: [15][  90/1109]	Loss 0.9572 (0.9366)	Accuracy 65.625 (65.586)
Epoch: [15][ 100/1109]	Loss 1.0106 (0.9375)	Accuracy 60.938 (65.397)
Epoch: [15][ 110/1109]	Loss 0.8304 (0.9350)	Accuracy 73.047 (65.498)
Epoch: [15][ 120/1109]	Loss 0.9150 (0.9337)	Accuracy 65.625 (65.583)
Epoch: [15][ 130/1109]	Loss 0.9263 (0.9353)	Accuracy 65.234 (65.547)
Epoch: [15][ 140/1109]	Loss 0.9782 (0.9335)	Accuracy 66.406 (65.658)
Epoch: [15][ 150/1109]	Loss 0.9691 (0.9337)	Accuracy 62.891 (65.653)
Epoch: [15][ 160/1109]	Loss 0.9506 (0.9332)	Accuracy 60.156 (65.654)
Epoch: [15][ 170/1109]	Loss 1.0193 (0.9339)	Accuracy 60.547 (65.584)
Epoch: [15][ 180/1109]	Loss 0.8351 (0.9341)	Accuracy 64.844 (65.515)
Epoch: [15][ 190/1109]	Loss 0.9175 (0.9352)	Accuracy 65.625 (65.476)
Epoch: [15][ 200/1109]	Loss 0.8991 (0.9345)	Accuracy 67.969 (65.522)
Epoch: [15][ 210/1109]	Loss 0.9288 (0.9342)	Accuracy 66.797 (65.553)
Epoch: [15][ 220/1109]	Loss 0.9948 (0.9343)	Accuracy 65.234 (65.616)
Epoch: [15][ 230/1109]	Loss 0.9853 (0.9326)	Accuracy 61.719 (65.647)
Epoch: [15][ 240/1109]	Loss 0.8984 (0.9316)	Accuracy 68.750 (65.693)
Epoch: [15][ 250/1109]	Loss 0.9293 (0.9323)	Accuracy 64.844 (65.678)
Epoch: [15][ 260/1109]	Loss 0.9769 (0.9318)	Accuracy 63.281 (65.682)
Epoch: [15][ 270/1109]	Loss 0.8276 (0.9320)	Accuracy 71.875 (65.660)
Epoch: [15][ 280/1109]	Loss 0.9024 (0.9315)	Accuracy 64.062 (65.667)
Epoch: [15][ 290/1109]	Loss 0.8113 (0.9317)	Accuracy 67.969 (65.655)
Epoch: [15][ 300/1109]	Loss 0.9217 (0.9322)	Accuracy 64.062 (65.603)
Epoch: [15][ 310/1109]	Loss 0.8071 (0.9326)	Accuracy 71.484 (65.584)
Epoch: [15][ 320/1109]	Loss 0.8987 (0.9317)	Accuracy 63.672 (65.612)
Epoch: [15][ 330/1109]	Loss 0.8456 (0.9314)	Accuracy 68.359 (65.650)
Epoch: [15][ 340/1109]	Loss 0.9180 (0.9308)	Accuracy 66.406 (65.696)
Epoch: [15][ 350/1109]	Loss 0.8127 (0.9308)	Accuracy 72.266 (65.707)
Epoch: [15][ 360/1109]	Loss 1.0157 (0.9307)	Accuracy 62.500 (65.715)
Epoch: [15][ 370/1109]	Loss 0.9255 (0.9303)	Accuracy 66.797 (65.765)
Epoch: [15][ 380/1109]	Loss 0.9233 (0.9302)	Accuracy 66.797 (65.765)
Epoch: [15][ 390/1109]	Loss 0.9485 (0.9305)	Accuracy 66.016 (65.769)
Epoch: [15][ 400/1109]	Loss 0.9648 (0.9300)	Accuracy 64.453 (65.760)
Epoch: [15][ 410/1109]	Loss 0.8929 (0.9301)	Accuracy 69.531 (65.748)
Epoch: [15][ 420/1109]	Loss 0.9744 (0.9307)	Accuracy 62.891 (65.715)
Epoch: [15][ 430/1109]	Loss 0.8613 (0.9299)	Accuracy 69.531 (65.746)
Epoch: [15][ 440/1109]	Loss 0.8696 (0.9294)	Accuracy 70.312 (65.758)
Epoch: [15][ 450/1109]	Loss 0.9915 (0.9293)	Accuracy 64.844 (65.773)
Epoch: [15][ 460/1109]	Loss 0.8902 (0.9291)	Accuracy 70.703 (65.784)
Epoch: [15][ 470/1109]	Loss 0.9202 (0.9292)	Accuracy 67.578 (65.794)
Epoch: [15][ 480/1109]	Loss 0.8702 (0.9291)	Accuracy 67.578 (65.798)
Epoch: [15][ 490/1109]	Loss 0.9092 (0.9291)	Accuracy 68.750 (65.808)
Epoch: [15][ 500/1109]	Loss 1.0561 (0.9297)	Accuracy 60.156 (65.777)
Epoch: [15][ 510/1109]	Loss 0.8774 (0.9301)	Accuracy 70.312 (65.763)
Epoch: [15][ 520/1109]	Loss 0.8691 (0.9299)	Accuracy 69.141 (65.786)
Epoch: [15][ 530/1109]	Loss 0.9177 (0.9303)	Accuracy 66.016 (65.779)
Epoch: [15][ 540/1109]	Loss 0.8888 (0.9305)	Accuracy 68.750 (65.764)
Epoch: [15][ 550/1109]	Loss 1.0330 (0.9306)	Accuracy 65.234 (65.775)
Epoch: [15][ 560/1109]	Loss 0.9159 (0.9304)	Accuracy 68.359 (65.783)
Epoch: [15][ 570/1109]	Loss 0.8528 (0.9303)	Accuracy 67.969 (65.793)
Epoch: [15][ 580/1109]	Loss 0.9307 (0.9301)	Accuracy 63.281 (65.782)
Epoch: [15][ 590/1109]	Loss 0.9520 (0.9303)	Accuracy 65.234 (65.789)
Epoch: [15][ 600/1109]	Loss 0.8907 (0.9300)	Accuracy 65.234 (65.798)
Epoch: [15][ 610/1109]	Loss 0.9557 (0.9298)	Accuracy 62.891 (65.806)
Epoch: [15][ 620/1109]	Loss 0.9066 (0.9294)	Accuracy 69.531 (65.828)
Epoch: [15][ 630/1109]	Loss 0.8776 (0.9292)	Accuracy 63.672 (65.834)
Epoch: [15][ 640/1109]	Loss 0.9421 (0.9292)	Accuracy 65.234 (65.827)
Epoch: [15][ 650/1109]	Loss 0.9102 (0.9288)	Accuracy 66.016 (65.852)
Epoch: [15][ 660/1109]	Loss 0.8964 (0.9287)	Accuracy 67.578 (65.859)
Epoch: [15][ 670/1109]	Loss 1.0138 (0.9290)	Accuracy 61.719 (65.850)
Epoch: [15][ 680/1109]	Loss 0.9242 (0.9289)	Accuracy 68.359 (65.854)
Epoch: [15][ 690/1109]	Loss 0.8747 (0.9286)	Accuracy 67.578 (65.858)
Epoch: [15][ 700/1109]	Loss 0.9415 (0.9283)	Accuracy 66.016 (65.865)
Epoch: [15][ 710/1109]	Loss 0.9625 (0.9288)	Accuracy 66.016 (65.851)
Epoch: [15][ 720/1109]	Loss 0.8952 (0.9287)	Accuracy 69.922 (65.860)
Epoch: [15][ 730/1109]	Loss 0.8931 (0.9287)	Accuracy 69.141 (65.871)
Epoch: [15][ 740/1109]	Loss 0.9620 (0.9290)	Accuracy 62.500 (65.872)
Epoch: [15][ 750/1109]	Loss 0.9770 (0.9289)	Accuracy 65.625 (65.890)
Epoch: [15][ 760/1109]	Loss 0.9155 (0.9288)	Accuracy 65.234 (65.894)
Epoch: [15][ 770/1109]	Loss 0.9338 (0.9291)	Accuracy 62.500 (65.883)
Epoch: [15][ 780/1109]	Loss 0.9628 (0.9295)	Accuracy 64.844 (65.869)
Epoch: [15][ 790/1109]	Loss 0.9895 (0.9291)	Accuracy 65.625 (65.889)
Epoch: [15][ 800/1109]	Loss 0.8929 (0.9293)	Accuracy 66.406 (65.885)
Epoch: [15][ 810/1109]	Loss 0.9075 (0.9295)	Accuracy 66.016 (65.893)
Epoch: [15][ 820/1109]	Loss 0.9185 (0.9299)	Accuracy 62.500 (65.877)
Epoch: [15][ 830/1109]	Loss 0.9753 (0.9301)	Accuracy 64.844 (65.860)
Epoch: [15][ 840/1109]	Loss 0.9525 (0.9300)	Accuracy 63.281 (65.849)
Epoch: [15][ 850/1109]	Loss 1.0120 (0.9302)	Accuracy 63.672 (65.843)
Epoch: [15][ 860/1109]	Loss 0.8903 (0.9302)	Accuracy 67.578 (65.841)
Epoch: [15][ 870/1109]	Loss 0.9340 (0.9301)	Accuracy 64.844 (65.849)
Epoch: [15][ 880/1109]	Loss 0.9644 (0.9303)	Accuracy 59.375 (65.838)
Epoch: [15][ 890/1109]	Loss 0.8477 (0.9304)	Accuracy 68.750 (65.833)
Epoch: [15][ 900/1109]	Loss 0.9464 (0.9302)	Accuracy 67.969 (65.843)
Epoch: [15][ 910/1109]	Loss 0.9058 (0.9305)	Accuracy 68.750 (65.838)
Epoch: [15][ 920/1109]	Loss 0.8502 (0.9300)	Accuracy 69.531 (65.856)
Epoch: [15][ 930/1109]	Loss 0.8561 (0.9295)	Accuracy 69.922 (65.872)
Epoch: [15][ 940/1109]	Loss 0.9069 (0.9293)	Accuracy 66.016 (65.883)
Epoch: [15][ 950/1109]	Loss 0.9358 (0.9294)	Accuracy 64.453 (65.882)
Epoch: [15][ 960/1109]	Loss 0.9112 (0.9295)	Accuracy 69.922 (65.876)
Epoch: [15][ 970/1109]	Loss 0.8634 (0.9293)	Accuracy 66.406 (65.878)
Epoch: [15][ 980/1109]	Loss 0.9555 (0.9291)	Accuracy 63.672 (65.883)
Epoch: [15][ 990/1109]	Loss 1.0060 (0.9294)	Accuracy 62.500 (65.869)
Epoch: [15][1000/1109]	Loss 0.9552 (0.9295)	Accuracy 67.188 (65.861)
Epoch: [15][1010/1109]	Loss 0.8436 (0.9296)	Accuracy 67.578 (65.853)
Epoch: [15][1020/1109]	Loss 0.9267 (0.9295)	Accuracy 66.406 (65.864)
Epoch: [15][1030/1109]	Loss 0.9525 (0.9296)	Accuracy 63.672 (65.854)
Epoch: [15][1040/1109]	Loss 0.8694 (0.9297)	Accuracy 70.312 (65.842)
Epoch: [15][1050/1109]	Loss 0.8855 (0.9296)	Accuracy 66.016 (65.841)
Epoch: [15][1060/1109]	Loss 1.0343 (0.9298)	Accuracy 63.672 (65.835)
Epoch: [15][1070/1109]	Loss 0.9361 (0.9297)	Accuracy 61.719 (65.833)
Epoch: [15][1080/1109]	Loss 0.8989 (0.9297)	Accuracy 66.797 (65.833)
Epoch: [15][1090/1109]	Loss 0.8797 (0.9293)	Accuracy 65.234 (65.852)
Epoch: [15][1100/1109]	Loss 0.7973 (0.9294)	Accuracy 71.875 (65.845)
Test: [ 0/14]	Loss 0.9535 (0.9535)	Accuracy 65.625 (65.625)
Test: [10/14]	Loss 2.0890 (1.0530)	Accuracy 47.266 (64.737)
 * Accuracy 63.847
Current best accuracy: 64.53272247314453
957.5848565101624
Current learning rate: 1.0000000000000003e-05
Epoch: [16][   0/1109]	Loss 0.9298 (0.9298)	Accuracy 71.484 (71.484)
Epoch: [16][  10/1109]	Loss 0.9487 (0.9138)	Accuracy 64.062 (66.903)
Epoch: [16][  20/1109]	Loss 0.9359 (0.9322)	Accuracy 64.453 (65.365)
Epoch: [16][  30/1109]	Loss 1.0178 (0.9344)	Accuracy 64.062 (65.222)
Epoch: [16][  40/1109]	Loss 0.9239 (0.9275)	Accuracy 66.797 (65.720)
Epoch: [16][  50/1109]	Loss 0.8504 (0.9265)	Accuracy 70.312 (65.954)
Epoch: [16][  60/1109]	Loss 0.9321 (0.9257)	Accuracy 64.453 (65.926)
Epoch: [16][  70/1109]	Loss 0.8972 (0.9230)	Accuracy 66.406 (65.999)
Epoch: [16][  80/1109]	Loss 0.9694 (0.9223)	Accuracy 64.453 (65.938)
Epoch: [16][  90/1109]	Loss 0.9533 (0.9275)	Accuracy 63.281 (65.681)
Epoch: [16][ 100/1109]	Loss 0.9927 (0.9302)	Accuracy 65.234 (65.637)
Epoch: [16][ 110/1109]	Loss 1.0098 (0.9306)	Accuracy 62.891 (65.720)
Epoch: [16][ 120/1109]	Loss 0.8691 (0.9292)	Accuracy 67.969 (65.780)
Epoch: [16][ 130/1109]	Loss 0.9191 (0.9273)	Accuracy 64.844 (65.798)
Epoch: [16][ 140/1109]	Loss 0.9006 (0.9279)	Accuracy 66.406 (65.788)
Epoch: [16][ 150/1109]	Loss 0.8978 (0.9271)	Accuracy 67.578 (65.837)
Epoch: [16][ 160/1109]	Loss 0.9169 (0.9275)	Accuracy 65.625 (65.805)
Epoch: [16][ 170/1109]	Loss 0.9564 (0.9284)	Accuracy 65.234 (65.808)
Epoch: [16][ 180/1109]	Loss 0.8398 (0.9271)	Accuracy 69.531 (65.867)
Epoch: [16][ 190/1109]	Loss 1.0004 (0.9287)	Accuracy 62.891 (65.776)
Epoch: [16][ 200/1109]	Loss 0.9996 (0.9287)	Accuracy 61.719 (65.761)
Epoch: [16][ 210/1109]	Loss 0.8963 (0.9279)	Accuracy 68.750 (65.799)
Epoch: [16][ 220/1109]	Loss 0.8637 (0.9274)	Accuracy 69.531 (65.821)
Epoch: [16][ 230/1109]	Loss 0.8828 (0.9277)	Accuracy 69.531 (65.862)
Epoch: [16][ 240/1109]	Loss 0.9586 (0.9290)	Accuracy 64.062 (65.813)
Epoch: [16][ 250/1109]	Loss 0.8957 (0.9293)	Accuracy 66.797 (65.863)
Epoch: [16][ 260/1109]	Loss 0.8347 (0.9280)	Accuracy 69.141 (65.909)
Epoch: [16][ 270/1109]	Loss 0.9785 (0.9281)	Accuracy 64.453 (65.900)
Epoch: [16][ 280/1109]	Loss 0.9960 (0.9296)	Accuracy 62.109 (65.836)
Epoch: [16][ 290/1109]	Loss 0.9108 (0.9302)	Accuracy 67.188 (65.828)
Epoch: [16][ 300/1109]	Loss 0.9346 (0.9290)	Accuracy 64.844 (65.883)
Epoch: [16][ 310/1109]	Loss 0.9354 (0.9287)	Accuracy 64.844 (65.880)
Epoch: [16][ 320/1109]	Loss 0.9023 (0.9291)	Accuracy 67.969 (65.848)
Epoch: [16][ 330/1109]	Loss 0.8598 (0.9286)	Accuracy 71.094 (65.879)
Epoch: [16][ 340/1109]	Loss 0.9074 (0.9292)	Accuracy 68.359 (65.850)
Epoch: [16][ 350/1109]	Loss 0.9874 (0.9290)	Accuracy 62.500 (65.859)
Epoch: [16][ 360/1109]	Loss 0.8711 (0.9287)	Accuracy 70.703 (65.913)
Epoch: [16][ 370/1109]	Loss 1.0100 (0.9293)	Accuracy 62.109 (65.916)
Epoch: [16][ 380/1109]	Loss 0.9922 (0.9295)	Accuracy 66.406 (65.931)
Epoch: [16][ 390/1109]	Loss 0.8649 (0.9298)	Accuracy 71.484 (65.916)
Epoch: [16][ 400/1109]	Loss 0.9142 (0.9304)	Accuracy 66.406 (65.891)
Epoch: [16][ 410/1109]	Loss 1.0278 (0.9297)	Accuracy 61.719 (65.914)
Epoch: [16][ 420/1109]	Loss 0.9104 (0.9298)	Accuracy 67.969 (65.907)
Epoch: [16][ 430/1109]	Loss 0.9910 (0.9306)	Accuracy 64.844 (65.875)
Epoch: [16][ 440/1109]	Loss 0.8466 (0.9299)	Accuracy 71.484 (65.909)
Epoch: [16][ 450/1109]	Loss 0.8984 (0.9305)	Accuracy 65.625 (65.874)
Epoch: [16][ 460/1109]	Loss 0.8845 (0.9304)	Accuracy 69.141 (65.872)
Epoch: [16][ 470/1109]	Loss 1.0333 (0.9305)	Accuracy 62.500 (65.866)
Epoch: [16][ 480/1109]	Loss 1.0139 (0.9307)	Accuracy 62.500 (65.865)
Epoch: [16][ 490/1109]	Loss 0.9223 (0.9305)	Accuracy 66.797 (65.858)
Epoch: [16][ 500/1109]	Loss 0.8682 (0.9306)	Accuracy 69.922 (65.841)
Epoch: [16][ 510/1109]	Loss 0.9823 (0.9311)	Accuracy 62.891 (65.833)
Epoch: [16][ 520/1109]	Loss 0.9515 (0.9312)	Accuracy 67.969 (65.822)
Epoch: [16][ 530/1109]	Loss 0.9985 (0.9311)	Accuracy 65.625 (65.834)
Epoch: [16][ 540/1109]	Loss 0.9458 (0.9316)	Accuracy 63.281 (65.810)
Epoch: [16][ 550/1109]	Loss 0.9267 (0.9314)	Accuracy 62.891 (65.805)
Epoch: [16][ 560/1109]	Loss 0.9649 (0.9312)	Accuracy 66.016 (65.843)
Epoch: [16][ 570/1109]	Loss 0.9971 (0.9312)	Accuracy 62.500 (65.839)
Epoch: [16][ 580/1109]	Loss 0.9458 (0.9311)	Accuracy 67.578 (65.833)
Epoch: [16][ 590/1109]	Loss 0.9044 (0.9316)	Accuracy 69.922 (65.825)
Epoch: [16][ 600/1109]	Loss 0.9142 (0.9319)	Accuracy 64.844 (65.810)
Epoch: [16][ 610/1109]	Loss 0.9671 (0.9317)	Accuracy 67.578 (65.817)
Epoch: [16][ 620/1109]	Loss 0.9685 (0.9317)	Accuracy 63.672 (65.811)
Epoch: [16][ 630/1109]	Loss 0.9508 (0.9317)	Accuracy 67.188 (65.824)
Epoch: [16][ 640/1109]	Loss 0.8911 (0.9315)	Accuracy 66.406 (65.830)
Epoch: [16][ 650/1109]	Loss 0.8744 (0.9313)	Accuracy 66.016 (65.830)
Epoch: [16][ 660/1109]	Loss 1.0287 (0.9315)	Accuracy 62.109 (65.829)
Epoch: [16][ 670/1109]	Loss 0.8869 (0.9314)	Accuracy 68.359 (65.831)
Epoch: [16][ 680/1109]	Loss 0.9764 (0.9314)	Accuracy 66.406 (65.836)
Epoch: [16][ 690/1109]	Loss 0.9977 (0.9310)	Accuracy 64.062 (65.849)
Epoch: [16][ 700/1109]	Loss 0.9490 (0.9309)	Accuracy 65.625 (65.851)
Epoch: [16][ 710/1109]	Loss 0.9801 (0.9312)	Accuracy 65.234 (65.834)
Epoch: [16][ 720/1109]	Loss 0.9638 (0.9311)	Accuracy 67.578 (65.850)
Epoch: [16][ 730/1109]	Loss 0.9749 (0.9315)	Accuracy 64.844 (65.832)
Epoch: [16][ 740/1109]	Loss 0.9164 (0.9314)	Accuracy 66.016 (65.834)
Epoch: [16][ 750/1109]	Loss 0.7911 (0.9310)	Accuracy 75.391 (65.855)
Epoch: [16][ 760/1109]	Loss 0.9128 (0.9310)	Accuracy 67.578 (65.868)
Epoch: [16][ 770/1109]	Loss 0.8557 (0.9310)	Accuracy 67.578 (65.863)
Epoch: [16][ 780/1109]	Loss 0.9256 (0.9309)	Accuracy 65.625 (65.857)
Epoch: [16][ 790/1109]	Loss 0.8795 (0.9309)	Accuracy 65.234 (65.848)
Epoch: [16][ 800/1109]	Loss 0.9924 (0.9312)	Accuracy 61.328 (65.846)
Epoch: [16][ 810/1109]	Loss 0.8635 (0.9311)	Accuracy 71.094 (65.862)
Epoch: [16][ 820/1109]	Loss 0.8995 (0.9312)	Accuracy 67.578 (65.861)
Epoch: [16][ 830/1109]	Loss 0.9231 (0.9311)	Accuracy 66.797 (65.868)
Epoch: [16][ 840/1109]	Loss 0.8847 (0.9311)	Accuracy 64.453 (65.849)
Epoch: [16][ 850/1109]	Loss 0.9274 (0.9314)	Accuracy 66.406 (65.839)
Epoch: [16][ 860/1109]	Loss 0.8984 (0.9319)	Accuracy 71.875 (65.825)
Epoch: [16][ 870/1109]	Loss 0.8843 (0.9317)	Accuracy 63.672 (65.835)
Epoch: [16][ 880/1109]	Loss 0.9555 (0.9317)	Accuracy 67.188 (65.843)
Epoch: [16][ 890/1109]	Loss 0.8942 (0.9319)	Accuracy 69.922 (65.842)
Epoch: [16][ 900/1109]	Loss 1.0296 (0.9320)	Accuracy 60.547 (65.837)
Epoch: [16][ 910/1109]	Loss 0.9070 (0.9317)	Accuracy 66.016 (65.850)
Epoch: [16][ 920/1109]	Loss 0.8861 (0.9317)	Accuracy 66.797 (65.846)
Epoch: [16][ 930/1109]	Loss 0.9785 (0.9317)	Accuracy 63.281 (65.841)
Epoch: [16][ 940/1109]	Loss 0.8963 (0.9315)	Accuracy 66.406 (65.856)
Epoch: [16][ 950/1109]	Loss 0.9600 (0.9318)	Accuracy 62.500 (65.846)
Epoch: [16][ 960/1109]	Loss 1.0368 (0.9320)	Accuracy 59.766 (65.829)
Epoch: [16][ 970/1109]	Loss 0.9516 (0.9322)	Accuracy 61.328 (65.812)
Epoch: [16][ 980/1109]	Loss 0.9786 (0.9321)	Accuracy 64.062 (65.812)
Epoch: [16][ 990/1109]	Loss 0.7895 (0.9320)	Accuracy 71.875 (65.826)
Epoch: [16][1000/1109]	Loss 0.9124 (0.9318)	Accuracy 65.234 (65.841)
Epoch: [16][1010/1109]	Loss 0.9268 (0.9318)	Accuracy 65.234 (65.841)
Epoch: [16][1020/1109]	Loss 0.8198 (0.9317)	Accuracy 69.141 (65.840)
Epoch: [16][1030/1109]	Loss 0.9244 (0.9316)	Accuracy 65.625 (65.836)
Epoch: [16][1040/1109]	Loss 0.9556 (0.9317)	Accuracy 69.141 (65.839)
Epoch: [16][1050/1109]	Loss 0.9598 (0.9313)	Accuracy 62.500 (65.854)
Epoch: [16][1060/1109]	Loss 0.9127 (0.9312)	Accuracy 64.062 (65.852)
Epoch: [16][1070/1109]	Loss 0.9820 (0.9313)	Accuracy 63.672 (65.852)
Epoch: [16][1080/1109]	Loss 0.9431 (0.9312)	Accuracy 66.406 (65.853)
Epoch: [16][1090/1109]	Loss 0.8536 (0.9311)	Accuracy 69.141 (65.849)
Epoch: [16][1100/1109]	Loss 1.0276 (0.9311)	Accuracy 60.938 (65.844)
Test: [ 0/14]	Loss 0.9824 (0.9824)	Accuracy 64.844 (64.844)
Test: [10/14]	Loss 2.1124 (1.0575)	Accuracy 46.875 (64.347)
 * Accuracy 63.618
Current best accuracy: 64.53272247314453
894.8052408695221
Current learning rate: 1.0000000000000003e-05
Epoch: [17][   0/1109]	Loss 1.0278 (1.0278)	Accuracy 59.375 (59.375)
Epoch: [17][  10/1109]	Loss 0.8501 (0.9337)	Accuracy 69.531 (64.915)
Epoch: [17][  20/1109]	Loss 1.0118 (0.9411)	Accuracy 62.891 (65.030)
Epoch: [17][  30/1109]	Loss 0.9393 (0.9440)	Accuracy 65.234 (65.360)
Epoch: [17][  40/1109]	Loss 0.8893 (0.9328)	Accuracy 66.797 (65.635)
Epoch: [17][  50/1109]	Loss 0.9382 (0.9342)	Accuracy 67.578 (65.472)
Epoch: [17][  60/1109]	Loss 0.8966 (0.9303)	Accuracy 65.625 (65.657)
Epoch: [17][  70/1109]	Loss 0.9346 (0.9271)	Accuracy 64.844 (65.812)
Epoch: [17][  80/1109]	Loss 0.8922 (0.9266)	Accuracy 67.578 (65.852)
Epoch: [17][  90/1109]	Loss 0.8866 (0.9257)	Accuracy 67.969 (66.119)
Epoch: [17][ 100/1109]	Loss 0.9281 (0.9287)	Accuracy 66.016 (66.097)
Epoch: [17][ 110/1109]	Loss 0.8751 (0.9278)	Accuracy 69.531 (66.058)
Epoch: [17][ 120/1109]	Loss 0.8839 (0.9285)	Accuracy 65.234 (65.932)
Epoch: [17][ 130/1109]	Loss 0.9595 (0.9297)	Accuracy 65.625 (65.881)
Epoch: [17][ 140/1109]	Loss 0.8979 (0.9289)	Accuracy 66.797 (66.013)
Epoch: [17][ 150/1109]	Loss 0.9790 (0.9295)	Accuracy 65.625 (65.972)
Epoch: [17][ 160/1109]	Loss 0.8554 (0.9296)	Accuracy 69.531 (65.926)
Epoch: [17][ 170/1109]	Loss 0.9199 (0.9284)	Accuracy 66.016 (66.000)
Epoch: [17][ 180/1109]	Loss 0.9540 (0.9282)	Accuracy 67.578 (66.018)
Epoch: [17][ 190/1109]	Loss 0.9062 (0.9297)	Accuracy 69.531 (65.944)
Epoch: [17][ 200/1109]	Loss 0.9168 (0.9309)	Accuracy 60.156 (65.839)
Epoch: [17][ 210/1109]	Loss 0.8925 (0.9310)	Accuracy 65.625 (65.849)
Epoch: [17][ 220/1109]	Loss 0.8444 (0.9305)	Accuracy 71.875 (65.943)
Epoch: [17][ 230/1109]	Loss 0.8248 (0.9303)	Accuracy 69.531 (65.943)
Epoch: [17][ 240/1109]	Loss 0.9284 (0.9312)	Accuracy 67.578 (65.952)
Epoch: [17][ 250/1109]	Loss 0.9371 (0.9302)	Accuracy 64.453 (66.013)
Epoch: [17][ 260/1109]	Loss 1.0096 (0.9293)	Accuracy 62.891 (65.998)
Epoch: [17][ 270/1109]	Loss 0.9441 (0.9291)	Accuracy 68.359 (66.055)
Epoch: [17][ 280/1109]	Loss 0.9603 (0.9283)	Accuracy 65.234 (66.093)
Epoch: [17][ 290/1109]	Loss 0.9900 (0.9283)	Accuracy 63.281 (66.111)
Epoch: [17][ 300/1109]	Loss 0.9539 (0.9275)	Accuracy 60.156 (66.138)
Epoch: [17][ 310/1109]	Loss 0.9702 (0.9284)	Accuracy 67.578 (66.109)
Epoch: [17][ 320/1109]	Loss 0.9809 (0.9288)	Accuracy 64.062 (66.079)
Epoch: [17][ 330/1109]	Loss 0.8916 (0.9291)	Accuracy 66.016 (66.082)
Epoch: [17][ 340/1109]	Loss 0.9838 (0.9296)	Accuracy 63.281 (66.077)
Epoch: [17][ 350/1109]	Loss 0.8491 (0.9299)	Accuracy 67.188 (66.051)
Epoch: [17][ 360/1109]	Loss 0.9279 (0.9309)	Accuracy 69.922 (66.031)
Epoch: [17][ 370/1109]	Loss 1.0366 (0.9307)	Accuracy 60.156 (66.017)
Epoch: [17][ 380/1109]	Loss 0.9629 (0.9312)	Accuracy 64.844 (65.994)
Epoch: [17][ 390/1109]	Loss 0.8647 (0.9303)	Accuracy 69.922 (66.038)
Epoch: [17][ 400/1109]	Loss 0.8860 (0.9305)	Accuracy 68.750 (66.059)
Epoch: [17][ 410/1109]	Loss 0.8579 (0.9308)	Accuracy 70.703 (66.083)
Epoch: [17][ 420/1109]	Loss 0.9363 (0.9315)	Accuracy 64.062 (66.064)
Epoch: [17][ 430/1109]	Loss 0.9856 (0.9316)	Accuracy 66.016 (66.042)
Epoch: [17][ 440/1109]	Loss 0.9803 (0.9320)	Accuracy 65.234 (66.019)
Epoch: [17][ 450/1109]	Loss 0.9621 (0.9322)	Accuracy 65.625 (66.004)
Epoch: [17][ 460/1109]	Loss 0.9805 (0.9326)	Accuracy 62.500 (65.961)
Epoch: [17][ 470/1109]	Loss 0.9020 (0.9326)	Accuracy 62.891 (65.964)
Epoch: [17][ 480/1109]	Loss 1.1448 (0.9335)	Accuracy 56.641 (65.925)
Epoch: [17][ 490/1109]	Loss 0.9776 (0.9340)	Accuracy 65.625 (65.903)
Epoch: [17][ 500/1109]	Loss 0.9373 (0.9341)	Accuracy 65.234 (65.888)
Epoch: [17][ 510/1109]	Loss 0.9413 (0.9343)	Accuracy 66.016 (65.864)
Epoch: [17][ 520/1109]	Loss 0.8997 (0.9345)	Accuracy 66.406 (65.859)
Epoch: [17][ 530/1109]	Loss 0.9564 (0.9344)	Accuracy 65.234 (65.874)
Epoch: [17][ 540/1109]	Loss 0.9830 (0.9341)	Accuracy 65.625 (65.902)
Epoch: [17][ 550/1109]	Loss 0.9667 (0.9342)	Accuracy 63.672 (65.878)
Epoch: [17][ 560/1109]	Loss 0.8348 (0.9344)	Accuracy 68.359 (65.865)
Epoch: [17][ 570/1109]	Loss 0.9944 (0.9349)	Accuracy 61.719 (65.851)
Epoch: [17][ 580/1109]	Loss 0.8999 (0.9347)	Accuracy 65.234 (65.844)
Epoch: [17][ 590/1109]	Loss 0.9526 (0.9342)	Accuracy 64.844 (65.859)
Epoch: [17][ 600/1109]	Loss 0.9286 (0.9343)	Accuracy 66.016 (65.836)
Epoch: [17][ 610/1109]	Loss 0.9626 (0.9344)	Accuracy 60.938 (65.821)
Epoch: [17][ 620/1109]	Loss 0.9645 (0.9348)	Accuracy 64.844 (65.798)
Epoch: [17][ 630/1109]	Loss 0.8459 (0.9344)	Accuracy 68.359 (65.806)
Epoch: [17][ 640/1109]	Loss 1.0096 (0.9343)	Accuracy 62.891 (65.807)
Epoch: [17][ 650/1109]	Loss 0.9297 (0.9337)	Accuracy 66.797 (65.837)
Epoch: [17][ 660/1109]	Loss 0.9774 (0.9338)	Accuracy 64.844 (65.829)
Epoch: [17][ 670/1109]	Loss 0.9080 (0.9335)	Accuracy 67.969 (65.837)
Epoch: [17][ 680/1109]	Loss 0.8996 (0.9336)	Accuracy 67.188 (65.851)
Epoch: [17][ 690/1109]	Loss 0.8208 (0.9334)	Accuracy 72.656 (65.864)
Epoch: [17][ 700/1109]	Loss 0.9390 (0.9335)	Accuracy 67.188 (65.871)
Epoch: [17][ 710/1109]	Loss 0.9716 (0.9336)	Accuracy 66.016 (65.878)
Epoch: [17][ 720/1109]	Loss 0.9971 (0.9335)	Accuracy 64.844 (65.866)
Epoch: [17][ 730/1109]	Loss 0.9248 (0.9337)	Accuracy 68.359 (65.847)
Epoch: [17][ 740/1109]	Loss 1.0024 (0.9339)	Accuracy 62.109 (65.837)
Epoch: [17][ 750/1109]	Loss 0.9090 (0.9339)	Accuracy 65.625 (65.833)
Epoch: [17][ 760/1109]	Loss 0.9192 (0.9336)	Accuracy 66.797 (65.850)
Epoch: [17][ 770/1109]	Loss 0.8659 (0.9331)	Accuracy 71.094 (65.876)
Epoch: [17][ 780/1109]	Loss 0.9323 (0.9333)	Accuracy 63.672 (65.852)
Epoch: [17][ 790/1109]	Loss 0.9577 (0.9335)	Accuracy 62.109 (65.844)
Epoch: [17][ 800/1109]	Loss 0.9705 (0.9333)	Accuracy 62.500 (65.859)
Epoch: [17][ 810/1109]	Loss 0.9947 (0.9335)	Accuracy 64.062 (65.850)
Epoch: [17][ 820/1109]	Loss 0.9077 (0.9335)	Accuracy 66.797 (65.839)
Epoch: [17][ 830/1109]	Loss 1.0228 (0.9333)	Accuracy 61.719 (65.837)
Epoch: [17][ 840/1109]	Loss 0.9369 (0.9331)	Accuracy 63.281 (65.834)
Epoch: [17][ 850/1109]	Loss 0.9498 (0.9327)	Accuracy 66.016 (65.840)
Epoch: [17][ 860/1109]	Loss 0.9298 (0.9329)	Accuracy 68.750 (65.831)
Epoch: [17][ 870/1109]	Loss 0.9777 (0.9329)	Accuracy 60.938 (65.820)
Epoch: [17][ 880/1109]	Loss 0.9943 (0.9329)	Accuracy 63.281 (65.813)
Epoch: [17][ 890/1109]	Loss 0.8242 (0.9326)	Accuracy 72.266 (65.828)
Epoch: [17][ 900/1109]	Loss 0.8507 (0.9324)	Accuracy 67.188 (65.828)
Epoch: [17][ 910/1109]	Loss 0.8953 (0.9324)	Accuracy 70.312 (65.829)
Epoch: [17][ 920/1109]	Loss 0.8712 (0.9323)	Accuracy 70.703 (65.836)
Epoch: [17][ 930/1109]	Loss 1.0236 (0.9324)	Accuracy 62.891 (65.833)
Epoch: [17][ 940/1109]	Loss 0.9714 (0.9328)	Accuracy 64.453 (65.809)
Epoch: [17][ 950/1109]	Loss 0.9633 (0.9325)	Accuracy 61.719 (65.810)
Epoch: [17][ 960/1109]	Loss 0.8630 (0.9326)	Accuracy 68.750 (65.809)
Epoch: [17][ 970/1109]	Loss 0.9319 (0.9324)	Accuracy 66.797 (65.811)
Epoch: [17][ 980/1109]	Loss 0.9664 (0.9325)	Accuracy 69.922 (65.809)
Epoch: [17][ 990/1109]	Loss 0.8998 (0.9324)	Accuracy 65.625 (65.806)
Epoch: [17][1000/1109]	Loss 0.9391 (0.9322)	Accuracy 68.359 (65.806)
Epoch: [17][1010/1109]	Loss 0.9189 (0.9323)	Accuracy 68.750 (65.801)
Epoch: [17][1020/1109]	Loss 0.8402 (0.9322)	Accuracy 69.141 (65.798)
Epoch: [17][1030/1109]	Loss 0.9401 (0.9322)	Accuracy 68.750 (65.791)
Epoch: [17][1040/1109]	Loss 0.9980 (0.9321)	Accuracy 60.938 (65.794)
Epoch: [17][1050/1109]	Loss 0.9536 (0.9322)	Accuracy 64.844 (65.789)
Epoch: [17][1060/1109]	Loss 0.8843 (0.9322)	Accuracy 69.922 (65.796)
Epoch: [17][1070/1109]	Loss 0.8279 (0.9323)	Accuracy 70.703 (65.796)
Epoch: [17][1080/1109]	Loss 0.8873 (0.9323)	Accuracy 68.359 (65.809)
Epoch: [17][1090/1109]	Loss 0.9038 (0.9323)	Accuracy 66.406 (65.809)
Epoch: [17][1100/1109]	Loss 0.9332 (0.9322)	Accuracy 67.188 (65.814)
Test: [ 0/14]	Loss 0.9528 (0.9528)	Accuracy 65.234 (65.234)
Test: [10/14]	Loss 2.1197 (1.0595)	Accuracy 46.484 (64.666)
 * Accuracy 63.790
Current best accuracy: 64.53272247314453
796.7942810058594
Current learning rate: 1.0000000000000003e-05
Epoch: [18][   0/1109]	Loss 1.0345 (1.0345)	Accuracy 62.500 (62.500)
Epoch: [18][  10/1109]	Loss 0.8919 (0.9551)	Accuracy 68.359 (64.915)
Epoch: [18][  20/1109]	Loss 0.9435 (0.9427)	Accuracy 66.016 (65.048)
Epoch: [18][  30/1109]	Loss 0.9115 (0.9357)	Accuracy 64.844 (65.524)
Epoch: [18][  40/1109]	Loss 0.9819 (0.9350)	Accuracy 64.844 (65.663)
Epoch: [18][  50/1109]	Loss 0.9827 (0.9270)	Accuracy 67.578 (65.954)
Epoch: [18][  60/1109]	Loss 0.9713 (0.9347)	Accuracy 60.938 (65.657)
Epoch: [18][  70/1109]	Loss 0.9623 (0.9366)	Accuracy 66.406 (65.570)
Epoch: [18][  80/1109]	Loss 1.0740 (0.9382)	Accuracy 58.984 (65.476)
Epoch: [18][  90/1109]	Loss 0.9416 (0.9378)	Accuracy 62.891 (65.526)
Epoch: [18][ 100/1109]	Loss 0.8856 (0.9373)	Accuracy 66.406 (65.567)
Epoch: [18][ 110/1109]	Loss 0.9489 (0.9373)	Accuracy 64.453 (65.632)
Epoch: [18][ 120/1109]	Loss 0.9298 (0.9362)	Accuracy 64.844 (65.635)
Epoch: [18][ 130/1109]	Loss 0.9808 (0.9365)	Accuracy 64.062 (65.664)
Epoch: [18][ 140/1109]	Loss 1.0364 (0.9380)	Accuracy 60.156 (65.561)
Epoch: [18][ 150/1109]	Loss 0.9558 (0.9396)	Accuracy 64.844 (65.514)
Epoch: [18][ 160/1109]	Loss 0.9649 (0.9373)	Accuracy 64.453 (65.589)
Epoch: [18][ 170/1109]	Loss 0.9581 (0.9356)	Accuracy 63.281 (65.673)
Epoch: [18][ 180/1109]	Loss 0.8655 (0.9352)	Accuracy 67.969 (65.685)
Epoch: [18][ 190/1109]	Loss 0.9540 (0.9341)	Accuracy 65.625 (65.699)
Epoch: [18][ 200/1109]	Loss 0.9485 (0.9347)	Accuracy 64.453 (65.642)
Epoch: [18][ 210/1109]	Loss 0.9139 (0.9347)	Accuracy 66.016 (65.684)
Epoch: [18][ 220/1109]	Loss 0.8713 (0.9348)	Accuracy 68.750 (65.678)
Epoch: [18][ 230/1109]	Loss 0.9205 (0.9355)	Accuracy 66.016 (65.588)
Epoch: [18][ 240/1109]	Loss 1.0143 (0.9364)	Accuracy 62.500 (65.520)
Epoch: [18][ 250/1109]	Loss 0.8415 (0.9360)	Accuracy 72.266 (65.560)
Epoch: [18][ 260/1109]	Loss 0.9554 (0.9351)	Accuracy 63.672 (65.562)
Epoch: [18][ 270/1109]	Loss 0.9108 (0.9349)	Accuracy 66.016 (65.564)
Epoch: [18][ 280/1109]	Loss 0.8541 (0.9341)	Accuracy 69.141 (65.604)
Epoch: [18][ 290/1109]	Loss 0.8749 (0.9342)	Accuracy 66.406 (65.606)
Epoch: [18][ 300/1109]	Loss 0.9136 (0.9340)	Accuracy 64.844 (65.621)
Epoch: [18][ 310/1109]	Loss 0.9924 (0.9342)	Accuracy 67.969 (65.616)
Epoch: [18][ 320/1109]	Loss 0.8839 (0.9343)	Accuracy 66.016 (65.638)
Epoch: [18][ 330/1109]	Loss 0.9580 (0.9350)	Accuracy 61.719 (65.629)
Epoch: [18][ 340/1109]	Loss 1.0787 (0.9352)	Accuracy 63.672 (65.633)
Epoch: [18][ 350/1109]	Loss 0.8905 (0.9353)	Accuracy 66.797 (65.594)
Epoch: [18][ 360/1109]	Loss 1.0285 (0.9349)	Accuracy 63.281 (65.599)
Epoch: [18][ 370/1109]	Loss 0.9263 (0.9346)	Accuracy 64.844 (65.590)
Epoch: [18][ 380/1109]	Loss 0.9817 (0.9351)	Accuracy 64.453 (65.582)
Epoch: [18][ 390/1109]	Loss 0.9627 (0.9347)	Accuracy 64.844 (65.574)
Epoch: [18][ 400/1109]	Loss 0.9902 (0.9341)	Accuracy 65.234 (65.600)
Epoch: [18][ 410/1109]	Loss 0.9794 (0.9337)	Accuracy 66.016 (65.606)
Epoch: [18][ 420/1109]	Loss 0.9160 (0.9337)	Accuracy 67.578 (65.628)
Epoch: [18][ 430/1109]	Loss 0.9834 (0.9338)	Accuracy 64.844 (65.625)
Epoch: [18][ 440/1109]	Loss 0.8923 (0.9337)	Accuracy 65.234 (65.631)
Epoch: [18][ 450/1109]	Loss 0.8941 (0.9331)	Accuracy 71.484 (65.676)
Epoch: [18][ 460/1109]	Loss 0.9349 (0.9329)	Accuracy 65.234 (65.678)
Epoch: [18][ 470/1109]	Loss 0.9050 (0.9329)	Accuracy 64.844 (65.684)
Epoch: [18][ 480/1109]	Loss 0.8328 (0.9330)	Accuracy 69.922 (65.693)
Epoch: [18][ 490/1109]	Loss 0.9236 (0.9329)	Accuracy 65.234 (65.709)
Epoch: [18][ 500/1109]	Loss 0.9192 (0.9324)	Accuracy 65.625 (65.735)
Epoch: [18][ 510/1109]	Loss 0.9554 (0.9321)	Accuracy 63.281 (65.753)
Epoch: [18][ 520/1109]	Loss 0.9914 (0.9322)	Accuracy 62.500 (65.758)
Epoch: [18][ 530/1109]	Loss 0.9970 (0.9320)	Accuracy 61.328 (65.738)
Epoch: [18][ 540/1109]	Loss 0.8853 (0.9315)	Accuracy 67.188 (65.746)
Epoch: [18][ 550/1109]	Loss 0.8306 (0.9311)	Accuracy 69.922 (65.757)
Epoch: [18][ 560/1109]	Loss 0.9180 (0.9313)	Accuracy 66.797 (65.745)
Epoch: [18][ 570/1109]	Loss 0.9247 (0.9308)	Accuracy 67.578 (65.763)
Epoch: [18][ 580/1109]	Loss 0.9066 (0.9307)	Accuracy 70.703 (65.779)
Epoch: [18][ 590/1109]	Loss 0.9121 (0.9309)	Accuracy 68.750 (65.792)
Epoch: [18][ 600/1109]	Loss 0.9675 (0.9308)	Accuracy 67.188 (65.795)
Epoch: [18][ 610/1109]	Loss 0.8534 (0.9312)	Accuracy 70.312 (65.798)
Epoch: [18][ 620/1109]	Loss 0.9658 (0.9315)	Accuracy 65.234 (65.795)
Epoch: [18][ 630/1109]	Loss 0.9606 (0.9322)	Accuracy 64.844 (65.769)
Epoch: [18][ 640/1109]	Loss 0.9237 (0.9318)	Accuracy 68.359 (65.771)
Epoch: [18][ 650/1109]	Loss 0.8725 (0.9316)	Accuracy 69.922 (65.810)
Epoch: [18][ 660/1109]	Loss 0.9801 (0.9316)	Accuracy 63.672 (65.820)
Epoch: [18][ 670/1109]	Loss 0.8913 (0.9316)	Accuracy 67.969 (65.818)
Epoch: [18][ 680/1109]	Loss 0.9540 (0.9318)	Accuracy 65.625 (65.809)
Epoch: [18][ 690/1109]	Loss 1.0699 (0.9314)	Accuracy 62.109 (65.829)
Epoch: [18][ 700/1109]	Loss 0.9702 (0.9316)	Accuracy 65.234 (65.814)
Epoch: [18][ 710/1109]	Loss 0.9809 (0.9313)	Accuracy 62.500 (65.810)
Epoch: [18][ 720/1109]	Loss 0.9430 (0.9314)	Accuracy 66.406 (65.809)
Epoch: [18][ 730/1109]	Loss 0.8533 (0.9317)	Accuracy 68.359 (65.797)
Epoch: [18][ 740/1109]	Loss 0.9412 (0.9315)	Accuracy 66.797 (65.802)
Epoch: [18][ 750/1109]	Loss 0.8981 (0.9313)	Accuracy 66.797 (65.805)
Epoch: [18][ 760/1109]	Loss 0.9726 (0.9313)	Accuracy 62.500 (65.819)
Epoch: [18][ 770/1109]	Loss 0.9306 (0.9312)	Accuracy 64.844 (65.826)
Epoch: [18][ 780/1109]	Loss 0.9549 (0.9314)	Accuracy 64.844 (65.811)
Epoch: [18][ 790/1109]	Loss 0.9430 (0.9321)	Accuracy 69.141 (65.795)
Epoch: [18][ 800/1109]	Loss 0.9546 (0.9325)	Accuracy 62.500 (65.774)
Epoch: [18][ 810/1109]	Loss 0.9663 (0.9324)	Accuracy 62.500 (65.782)
Epoch: [18][ 820/1109]	Loss 0.9491 (0.9325)	Accuracy 65.625 (65.784)
Epoch: [18][ 830/1109]	Loss 0.9189 (0.9328)	Accuracy 67.969 (65.763)
Epoch: [18][ 840/1109]	Loss 0.8830 (0.9325)	Accuracy 66.016 (65.766)
Epoch: [18][ 850/1109]	Loss 0.9113 (0.9324)	Accuracy 63.281 (65.770)
Epoch: [18][ 860/1109]	Loss 0.8926 (0.9324)	Accuracy 69.141 (65.773)
Epoch: [18][ 870/1109]	Loss 0.9859 (0.9322)	Accuracy 61.328 (65.779)
Epoch: [18][ 880/1109]	Loss 0.8637 (0.9320)	Accuracy 66.797 (65.787)
Epoch: [18][ 890/1109]	Loss 0.9823 (0.9321)	Accuracy 62.891 (65.785)
Epoch: [18][ 900/1109]	Loss 0.9457 (0.9323)	Accuracy 58.594 (65.769)
Epoch: [18][ 910/1109]	Loss 0.9756 (0.9321)	Accuracy 62.500 (65.775)
Epoch: [18][ 920/1109]	Loss 0.8201 (0.9322)	Accuracy 71.094 (65.770)
Epoch: [18][ 930/1109]	Loss 1.0454 (0.9329)	Accuracy 60.156 (65.749)
Epoch: [18][ 940/1109]	Loss 0.9447 (0.9330)	Accuracy 63.281 (65.738)
Epoch: [18][ 950/1109]	Loss 0.8573 (0.9328)	Accuracy 70.312 (65.745)
Epoch: [18][ 960/1109]	Loss 0.9166 (0.9326)	Accuracy 63.672 (65.751)
Epoch: [18][ 970/1109]	Loss 0.9049 (0.9326)	Accuracy 65.625 (65.746)
Epoch: [18][ 980/1109]	Loss 0.9186 (0.9326)	Accuracy 69.141 (65.749)
Epoch: [18][ 990/1109]	Loss 1.0375 (0.9324)	Accuracy 65.234 (65.761)
Epoch: [18][1000/1109]	Loss 0.9404 (0.9322)	Accuracy 66.406 (65.765)
Epoch: [18][1010/1109]	Loss 0.8906 (0.9320)	Accuracy 69.141 (65.777)
Epoch: [18][1020/1109]	Loss 0.9851 (0.9320)	Accuracy 67.188 (65.791)
Epoch: [18][1030/1109]	Loss 0.8935 (0.9318)	Accuracy 65.234 (65.791)
Epoch: [18][1040/1109]	Loss 0.9305 (0.9316)	Accuracy 64.844 (65.794)
Epoch: [18][1050/1109]	Loss 0.9854 (0.9317)	Accuracy 67.188 (65.804)
Epoch: [18][1060/1109]	Loss 0.9635 (0.9316)	Accuracy 63.281 (65.794)
Epoch: [18][1070/1109]	Loss 0.9558 (0.9316)	Accuracy 63.672 (65.800)
Epoch: [18][1080/1109]	Loss 0.9138 (0.9316)	Accuracy 66.406 (65.805)
Epoch: [18][1090/1109]	Loss 0.9231 (0.9316)	Accuracy 64.844 (65.803)
Epoch: [18][1100/1109]	Loss 0.8691 (0.9316)	Accuracy 71.484 (65.810)
Test: [ 0/14]	Loss 0.9909 (0.9909)	Accuracy 64.844 (64.844)
Test: [10/14]	Loss 2.0700 (1.0525)	Accuracy 47.266 (64.595)
 * Accuracy 63.790
Current best accuracy: 64.53272247314453
808.0558061599731
Current learning rate: 1.0000000000000003e-05
Epoch: [19][   0/1109]	Loss 0.9922 (0.9922)	Accuracy 65.234 (65.234)
Epoch: [19][  10/1109]	Loss 0.8859 (0.9018)	Accuracy 66.797 (67.294)
Epoch: [19][  20/1109]	Loss 0.9684 (0.9204)	Accuracy 67.188 (66.518)
Epoch: [19][  30/1109]	Loss 1.0165 (0.9213)	Accuracy 60.938 (66.041)
Epoch: [19][  40/1109]	Loss 0.9718 (0.9189)	Accuracy 63.281 (65.901)
Epoch: [19][  50/1109]	Loss 0.8844 (0.9149)	Accuracy 66.406 (66.100)
Epoch: [19][  60/1109]	Loss 0.9117 (0.9187)	Accuracy 68.750 (66.092)
Epoch: [19][  70/1109]	Loss 0.9010 (0.9185)	Accuracy 65.625 (66.131)
Epoch: [19][  80/1109]	Loss 1.1066 (0.9209)	Accuracy 56.641 (66.016)
Epoch: [19][  90/1109]	Loss 0.8921 (0.9247)	Accuracy 67.969 (65.900)
Epoch: [19][ 100/1109]	Loss 0.9806 (0.9268)	Accuracy 66.016 (65.772)
Epoch: [19][ 110/1109]	Loss 0.8996 (0.9275)	Accuracy 68.359 (65.724)
Epoch: [19][ 120/1109]	Loss 0.9398 (0.9269)	Accuracy 68.359 (65.861)
Epoch: [19][ 130/1109]	Loss 0.9980 (0.9308)	Accuracy 62.500 (65.697)
Epoch: [19][ 140/1109]	Loss 0.9910 (0.9325)	Accuracy 61.328 (65.606)
Epoch: [19][ 150/1109]	Loss 0.9372 (0.9335)	Accuracy 70.703 (65.576)
Epoch: [19][ 160/1109]	Loss 0.9717 (0.9345)	Accuracy 62.109 (65.523)
Epoch: [19][ 170/1109]	Loss 0.9507 (0.9345)	Accuracy 66.016 (65.575)
Epoch: [19][ 180/1109]	Loss 0.9269 (0.9349)	Accuracy 66.016 (65.528)
Epoch: [19][ 190/1109]	Loss 0.8736 (0.9349)	Accuracy 69.922 (65.521)
Epoch: [19][ 200/1109]	Loss 0.9109 (0.9340)	Accuracy 69.531 (65.569)
Epoch: [19][ 210/1109]	Loss 1.0805 (0.9334)	Accuracy 55.469 (65.573)
Epoch: [19][ 220/1109]	Loss 0.9463 (0.9342)	Accuracy 64.844 (65.556)
Epoch: [19][ 230/1109]	Loss 0.9333 (0.9341)	Accuracy 68.750 (65.559)
Epoch: [19][ 240/1109]	Loss 0.9603 (0.9350)	Accuracy 64.062 (65.536)
Epoch: [19][ 250/1109]	Loss 0.9078 (0.9330)	Accuracy 66.406 (65.658)
Epoch: [19][ 260/1109]	Loss 0.9416 (0.9340)	Accuracy 66.797 (65.650)
Epoch: [19][ 270/1109]	Loss 1.0388 (0.9351)	Accuracy 62.891 (65.624)
Epoch: [19][ 280/1109]	Loss 0.8703 (0.9347)	Accuracy 66.797 (65.639)
Epoch: [19][ 290/1109]	Loss 1.0065 (0.9348)	Accuracy 60.156 (65.604)
Epoch: [19][ 300/1109]	Loss 0.8482 (0.9343)	Accuracy 66.797 (65.625)
Epoch: [19][ 310/1109]	Loss 0.9342 (0.9350)	Accuracy 64.844 (65.633)
Epoch: [19][ 320/1109]	Loss 0.9290 (0.9362)	Accuracy 65.234 (65.609)
Epoch: [19][ 330/1109]	Loss 0.9776 (0.9366)	Accuracy 66.406 (65.587)
Epoch: [19][ 340/1109]	Loss 0.9677 (0.9369)	Accuracy 63.281 (65.577)
Epoch: [19][ 350/1109]	Loss 0.8886 (0.9367)	Accuracy 69.922 (65.568)
Epoch: [19][ 360/1109]	Loss 0.8732 (0.9363)	Accuracy 68.750 (65.572)
Epoch: [19][ 370/1109]	Loss 0.9594 (0.9363)	Accuracy 62.891 (65.580)
Epoch: [19][ 380/1109]	Loss 0.9088 (0.9366)	Accuracy 65.625 (65.566)
Epoch: [19][ 390/1109]	Loss 0.9374 (0.9365)	Accuracy 64.453 (65.576)
Epoch: [19][ 400/1109]	Loss 0.9568 (0.9362)	Accuracy 64.062 (65.569)
Epoch: [19][ 410/1109]	Loss 0.9569 (0.9362)	Accuracy 63.281 (65.532)
Epoch: [19][ 420/1109]	Loss 1.0240 (0.9364)	Accuracy 62.500 (65.519)
Epoch: [19][ 430/1109]	Loss 0.9223 (0.9357)	Accuracy 66.406 (65.577)
Epoch: [19][ 440/1109]	Loss 0.9638 (0.9360)	Accuracy 64.844 (65.566)
Epoch: [19][ 450/1109]	Loss 1.0160 (0.9359)	Accuracy 60.156 (65.555)
Epoch: [19][ 460/1109]	Loss 0.7924 (0.9347)	Accuracy 71.484 (65.623)
Epoch: [19][ 470/1109]	Loss 1.0149 (0.9348)	Accuracy 60.156 (65.612)
Epoch: [19][ 480/1109]	Loss 0.9517 (0.9351)	Accuracy 65.625 (65.611)
Epoch: [19][ 490/1109]	Loss 1.0519 (0.9350)	Accuracy 60.938 (65.608)
Epoch: [19][ 500/1109]	Loss 0.9747 (0.9347)	Accuracy 62.891 (65.613)
Epoch: [19][ 510/1109]	Loss 0.9213 (0.9349)	Accuracy 65.234 (65.610)
Epoch: [19][ 520/1109]	Loss 0.8961 (0.9343)	Accuracy 71.094 (65.640)
Epoch: [19][ 530/1109]	Loss 0.9973 (0.9350)	Accuracy 60.938 (65.602)
Epoch: [19][ 540/1109]	Loss 0.8795 (0.9352)	Accuracy 68.750 (65.591)
Epoch: [19][ 550/1109]	Loss 0.9504 (0.9349)	Accuracy 65.625 (65.591)
Epoch: [19][ 560/1109]	Loss 0.9943 (0.9353)	Accuracy 62.500 (65.584)
Epoch: [19][ 570/1109]	Loss 1.0225 (0.9349)	Accuracy 60.938 (65.609)
Epoch: [19][ 580/1109]	Loss 0.8883 (0.9347)	Accuracy 69.141 (65.622)
Epoch: [19][ 590/1109]	Loss 0.9021 (0.9345)	Accuracy 66.406 (65.615)
Epoch: [19][ 600/1109]	Loss 0.8834 (0.9344)	Accuracy 67.188 (65.613)
Epoch: [19][ 610/1109]	Loss 0.9506 (0.9342)	Accuracy 63.672 (65.600)
Epoch: [19][ 620/1109]	Loss 0.9458 (0.9346)	Accuracy 64.062 (65.597)
Epoch: [19][ 630/1109]	Loss 0.9533 (0.9346)	Accuracy 68.359 (65.612)
Epoch: [19][ 640/1109]	Loss 0.9647 (0.9344)	Accuracy 65.234 (65.647)
Epoch: [19][ 650/1109]	Loss 0.8575 (0.9343)	Accuracy 67.969 (65.644)
Epoch: [19][ 660/1109]	Loss 0.9944 (0.9340)	Accuracy 65.234 (65.664)
Epoch: [19][ 670/1109]	Loss 1.0113 (0.9342)	Accuracy 61.719 (65.653)
Epoch: [19][ 680/1109]	Loss 0.9824 (0.9345)	Accuracy 62.500 (65.640)
Epoch: [19][ 690/1109]	Loss 0.9020 (0.9345)	Accuracy 70.312 (65.637)
Epoch: [19][ 700/1109]	Loss 0.8934 (0.9346)	Accuracy 68.359 (65.645)
Epoch: [19][ 710/1109]	Loss 1.0121 (0.9348)	Accuracy 62.891 (65.640)
Epoch: [19][ 720/1109]	Loss 0.8516 (0.9346)	Accuracy 68.750 (65.646)
Epoch: [19][ 730/1109]	Loss 0.9060 (0.9345)	Accuracy 64.453 (65.641)
Epoch: [19][ 740/1109]	Loss 1.0332 (0.9344)	Accuracy 66.406 (65.650)
Epoch: [19][ 750/1109]	Loss 0.9179 (0.9342)	Accuracy 64.062 (65.648)
Epoch: [19][ 760/1109]	Loss 0.9335 (0.9340)	Accuracy 67.188 (65.670)
Epoch: [19][ 770/1109]	Loss 0.7909 (0.9341)	Accuracy 74.609 (65.676)
Epoch: [19][ 780/1109]	Loss 0.8764 (0.9342)	Accuracy 66.797 (65.670)
Epoch: [19][ 790/1109]	Loss 0.9260 (0.9346)	Accuracy 66.797 (65.662)
Epoch: [19][ 800/1109]	Loss 0.9246 (0.9341)	Accuracy 66.797 (65.686)
Epoch: [19][ 810/1109]	Loss 0.9085 (0.9338)	Accuracy 68.359 (65.693)
Epoch: [19][ 820/1109]	Loss 0.9243 (0.9337)	Accuracy 66.016 (65.699)
Epoch: [19][ 830/1109]	Loss 0.8959 (0.9339)	Accuracy 65.625 (65.684)
Epoch: [19][ 840/1109]	Loss 0.9713 (0.9339)	Accuracy 63.672 (65.686)
Epoch: [19][ 850/1109]	Loss 0.8266 (0.9336)	Accuracy 72.266 (65.705)
Epoch: [19][ 860/1109]	Loss 0.9073 (0.9339)	Accuracy 67.578 (65.697)
Epoch: [19][ 870/1109]	Loss 0.9711 (0.9339)	Accuracy 61.328 (65.693)
Epoch: [19][ 880/1109]	Loss 0.8493 (0.9337)	Accuracy 69.141 (65.697)
Epoch: [19][ 890/1109]	Loss 0.8901 (0.9333)	Accuracy 67.578 (65.706)
Epoch: [19][ 900/1109]	Loss 0.8521 (0.9333)	Accuracy 66.797 (65.707)
Epoch: [19][ 910/1109]	Loss 0.8566 (0.9332)	Accuracy 66.797 (65.710)
Epoch: [19][ 920/1109]	Loss 1.0779 (0.9334)	Accuracy 59.375 (65.709)
Epoch: [19][ 930/1109]	Loss 0.8903 (0.9332)	Accuracy 66.406 (65.706)
Epoch: [19][ 940/1109]	Loss 0.7839 (0.9331)	Accuracy 70.312 (65.705)
Epoch: [19][ 950/1109]	Loss 0.9504 (0.9330)	Accuracy 62.891 (65.715)
Epoch: [19][ 960/1109]	Loss 0.9645 (0.9331)	Accuracy 61.328 (65.712)
Epoch: [19][ 970/1109]	Loss 0.9889 (0.9332)	Accuracy 64.062 (65.700)
Epoch: [19][ 980/1109]	Loss 1.0047 (0.9333)	Accuracy 62.891 (65.701)
Epoch: [19][ 990/1109]	Loss 1.0187 (0.9333)	Accuracy 63.281 (65.709)
Epoch: [19][1000/1109]	Loss 0.8595 (0.9333)	Accuracy 70.703 (65.704)
Epoch: [19][1010/1109]	Loss 0.8019 (0.9332)	Accuracy 68.359 (65.707)
Epoch: [19][1020/1109]	Loss 0.9559 (0.9332)	Accuracy 65.234 (65.707)
Epoch: [19][1030/1109]	Loss 0.8478 (0.9329)	Accuracy 71.875 (65.723)
Epoch: [19][1040/1109]	Loss 1.0453 (0.9326)	Accuracy 63.672 (65.739)
Epoch: [19][1050/1109]	Loss 1.0175 (0.9324)	Accuracy 62.891 (65.748)
Epoch: [19][1060/1109]	Loss 1.0481 (0.9323)	Accuracy 66.797 (65.754)
Epoch: [19][1070/1109]	Loss 0.9365 (0.9324)	Accuracy 64.844 (65.750)
Epoch: [19][1080/1109]	Loss 0.9882 (0.9322)	Accuracy 63.281 (65.750)
Epoch: [19][1090/1109]	Loss 0.8756 (0.9324)	Accuracy 70.703 (65.741)
Epoch: [19][1100/1109]	Loss 0.8521 (0.9320)	Accuracy 67.969 (65.762)
Test: [ 0/14]	Loss 0.9735 (0.9735)	Accuracy 65.234 (65.234)
Test: [10/14]	Loss 2.0958 (1.0513)	Accuracy 46.875 (64.915)
 * Accuracy 63.933
Current best accuracy: 64.53272247314453
800.89288854599

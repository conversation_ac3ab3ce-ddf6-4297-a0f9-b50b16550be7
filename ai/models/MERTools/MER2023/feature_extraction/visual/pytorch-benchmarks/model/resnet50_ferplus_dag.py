# *_*coding:utf-8 *_*

import torch
import torch.nn as nn


class Resnet50_ferplus_dag(nn.Module):

    def __init__(self):
        super(Resnet50_ferplus_dag, self).__init__()
        self.meta = {
            "mean": [131.0912, 103.8827, 91.4953],
            "std": [1, 1, 1],
            "imageSize": [224, 224, 3],
        }

        from collections import OrderedDict

        self.debug_feats = OrderedDict()  # only used for feature verification
        self.conv1_7x7_s2 = nn.Conv2d(
            3, 64, kernel_size=[7, 7], stride=(2, 2), padding=(3, 3), bias=False
        )
        self.conv1_7x7_s2_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv1_relu_7x7_s2 = nn.ReLU()
        self.pool1_3x3_s2 = nn.MaxPool2d(
            kernel_size=[3, 3], stride=[2, 2], padding=(0, 0), dilation=1, ceil_mode=True
        )
        self.conv2_1_1x1_reduce = nn.Conv2d(64, 64, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv2_1_1x1_reduce_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_1_1x1_reduce_relu = nn.ReLU()
        self.conv2_1_3x3 = nn.Conv2d(
            64, 64, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv2_1_3x3_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_1_3x3_relu = nn.ReLU()
        self.conv2_1_1x1_increase = nn.Conv2d(
            64, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv2_1_1x1_proj = nn.Conv2d(64, 256, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv2_1_1x1_increase_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_1_1x1_proj_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_1_relu = nn.ReLU()
        self.conv2_2_1x1_reduce = nn.Conv2d(256, 64, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv2_2_1x1_reduce_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_2_1x1_reduce_relu = nn.ReLU()
        self.conv2_2_3x3 = nn.Conv2d(
            64, 64, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv2_2_3x3_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_2_3x3_relu = nn.ReLU()
        self.conv2_2_1x1_increase = nn.Conv2d(
            64, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv2_2_1x1_increase_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_2_relu = nn.ReLU()
        self.conv2_3_1x1_reduce = nn.Conv2d(256, 64, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv2_3_1x1_reduce_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_3_1x1_reduce_relu = nn.ReLU()
        self.conv2_3_3x3 = nn.Conv2d(
            64, 64, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv2_3_3x3_bn = nn.BatchNorm2d(
            64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_3_3x3_relu = nn.ReLU()
        self.conv2_3_1x1_increase = nn.Conv2d(
            64, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv2_3_1x1_increase_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv2_3_relu = nn.ReLU()
        self.conv3_1_1x1_reduce = nn.Conv2d(256, 128, kernel_size=[1, 1], stride=(2, 2), bias=False)
        self.conv3_1_1x1_reduce_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_1_1x1_reduce_relu = nn.ReLU()
        self.conv3_1_3x3 = nn.Conv2d(
            128, 128, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv3_1_3x3_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_1_3x3_relu = nn.ReLU()
        self.conv3_1_1x1_increase = nn.Conv2d(
            128, 512, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv3_1_1x1_proj = nn.Conv2d(256, 512, kernel_size=[1, 1], stride=(2, 2), bias=False)
        self.conv3_1_1x1_increase_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_1_1x1_proj_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_1_relu = nn.ReLU()
        self.conv3_2_1x1_reduce = nn.Conv2d(512, 128, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv3_2_1x1_reduce_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_2_1x1_reduce_relu = nn.ReLU()
        self.conv3_2_3x3 = nn.Conv2d(
            128, 128, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv3_2_3x3_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_2_3x3_relu = nn.ReLU()
        self.conv3_2_1x1_increase = nn.Conv2d(
            128, 512, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv3_2_1x1_increase_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_2_relu = nn.ReLU()
        self.conv3_3_1x1_reduce = nn.Conv2d(512, 128, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv3_3_1x1_reduce_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_3_1x1_reduce_relu = nn.ReLU()
        self.conv3_3_3x3 = nn.Conv2d(
            128, 128, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv3_3_3x3_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_3_3x3_relu = nn.ReLU()
        self.conv3_3_1x1_increase = nn.Conv2d(
            128, 512, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv3_3_1x1_increase_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_3_relu = nn.ReLU()
        self.conv3_4_1x1_reduce = nn.Conv2d(512, 128, kernel_size=[1, 1], stride=(1, 1), bias=False)
        self.conv3_4_1x1_reduce_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_4_1x1_reduce_relu = nn.ReLU()
        self.conv3_4_3x3 = nn.Conv2d(
            128, 128, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv3_4_3x3_bn = nn.BatchNorm2d(
            128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_4_3x3_relu = nn.ReLU()
        self.conv3_4_1x1_increase = nn.Conv2d(
            128, 512, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv3_4_1x1_increase_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv3_4_relu = nn.ReLU()
        self.conv4_1_1x1_reduce = nn.Conv2d(512, 256, kernel_size=[1, 1], stride=(2, 2), bias=False)
        self.conv4_1_1x1_reduce_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_1_1x1_reduce_relu = nn.ReLU()
        self.conv4_1_3x3 = nn.Conv2d(
            256, 256, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv4_1_3x3_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_1_3x3_relu = nn.ReLU()
        self.conv4_1_1x1_increase = nn.Conv2d(
            256, 1024, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_1_1x1_proj = nn.Conv2d(512, 1024, kernel_size=[1, 1], stride=(2, 2), bias=False)
        self.conv4_1_1x1_increase_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_1_1x1_proj_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_1_relu = nn.ReLU()
        self.conv4_2_1x1_reduce = nn.Conv2d(
            1024, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_2_1x1_reduce_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_2_1x1_reduce_relu = nn.ReLU()
        self.conv4_2_3x3 = nn.Conv2d(
            256, 256, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv4_2_3x3_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_2_3x3_relu = nn.ReLU()
        self.conv4_2_1x1_increase = nn.Conv2d(
            256, 1024, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_2_1x1_increase_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_2_relu = nn.ReLU()
        self.conv4_3_1x1_reduce = nn.Conv2d(
            1024, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_3_1x1_reduce_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_3_1x1_reduce_relu = nn.ReLU()
        self.conv4_3_3x3 = nn.Conv2d(
            256, 256, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv4_3_3x3_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_3_3x3_relu = nn.ReLU()
        self.conv4_3_1x1_increase = nn.Conv2d(
            256, 1024, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_3_1x1_increase_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_3_relu = nn.ReLU()
        self.conv4_4_1x1_reduce = nn.Conv2d(
            1024, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_4_1x1_reduce_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_4_1x1_reduce_relu = nn.ReLU()
        self.conv4_4_3x3 = nn.Conv2d(
            256, 256, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv4_4_3x3_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_4_3x3_relu = nn.ReLU()
        self.conv4_4_1x1_increase = nn.Conv2d(
            256, 1024, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_4_1x1_increase_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_4_relu = nn.ReLU()
        self.conv4_5_1x1_reduce = nn.Conv2d(
            1024, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_5_1x1_reduce_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_5_1x1_reduce_relu = nn.ReLU()
        self.conv4_5_3x3 = nn.Conv2d(
            256, 256, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv4_5_3x3_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_5_3x3_relu = nn.ReLU()
        self.conv4_5_1x1_increase = nn.Conv2d(
            256, 1024, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_5_1x1_increase_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_5_relu = nn.ReLU()
        self.conv4_6_1x1_reduce = nn.Conv2d(
            1024, 256, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_6_1x1_reduce_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_6_1x1_reduce_relu = nn.ReLU()
        self.conv4_6_3x3 = nn.Conv2d(
            256, 256, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv4_6_3x3_bn = nn.BatchNorm2d(
            256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_6_3x3_relu = nn.ReLU()
        self.conv4_6_1x1_increase = nn.Conv2d(
            256, 1024, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv4_6_1x1_increase_bn = nn.BatchNorm2d(
            1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv4_6_relu = nn.ReLU()
        self.conv5_1_1x1_reduce = nn.Conv2d(
            1024, 512, kernel_size=[1, 1], stride=(2, 2), bias=False
        )
        self.conv5_1_1x1_reduce_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_1_1x1_reduce_relu = nn.ReLU()
        self.conv5_1_3x3 = nn.Conv2d(
            512, 512, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv5_1_3x3_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_1_3x3_relu = nn.ReLU()
        self.conv5_1_1x1_increase = nn.Conv2d(
            512, 2048, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv5_1_1x1_proj = nn.Conv2d(1024, 2048, kernel_size=[1, 1], stride=(2, 2), bias=False)
        self.conv5_1_1x1_increase_bn = nn.BatchNorm2d(
            2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_1_1x1_proj_bn = nn.BatchNorm2d(
            2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_1_relu = nn.ReLU()
        self.conv5_2_1x1_reduce = nn.Conv2d(
            2048, 512, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv5_2_1x1_reduce_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_2_1x1_reduce_relu = nn.ReLU()
        self.conv5_2_3x3 = nn.Conv2d(
            512, 512, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv5_2_3x3_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_2_3x3_relu = nn.ReLU()
        self.conv5_2_1x1_increase = nn.Conv2d(
            512, 2048, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv5_2_1x1_increase_bn = nn.BatchNorm2d(
            2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_2_relu = nn.ReLU()
        self.conv5_3_1x1_reduce = nn.Conv2d(
            2048, 512, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv5_3_1x1_reduce_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_3_1x1_reduce_relu = nn.ReLU()
        self.conv5_3_3x3 = nn.Conv2d(
            512, 512, kernel_size=[3, 3], stride=(1, 1), padding=(1, 1), bias=False
        )
        self.conv5_3_3x3_drop = nn.Dropout(p=0.5)
        self.conv5_3_3x3_bn = nn.BatchNorm2d(
            512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_3_3x3_relu = nn.ReLU()
        self.conv5_3_1x1_increase = nn.Conv2d(
            512, 2048, kernel_size=[1, 1], stride=(1, 1), bias=False
        )
        self.conv5_3_1x1_increase_drop = nn.Dropout(p=0.5)
        self.conv5_3_1x1_increase_bn = nn.BatchNorm2d(
            2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True
        )
        self.conv5_3_relu = nn.ReLU()
        self.pool5_7x7_s1 = nn.AvgPool2d(kernel_size=[7, 7], stride=[1, 1], padding=0)
        self.classifier = nn.Conv2d(2048, 8, kernel_size=[1, 1], stride=(1, 1))

    def forward(self, data):
        conv1_7x7_s2 = self.conv1_7x7_s2(data)
        conv1_7x7_s2_bn = self.conv1_7x7_s2_bn(conv1_7x7_s2)
        conv1_7x7_s2_bnxx = self.conv1_relu_7x7_s2(conv1_7x7_s2_bn)
        pool1_3x3_s2 = self.pool1_3x3_s2(conv1_7x7_s2_bnxx)
        conv2_1_1x1_reduce = self.conv2_1_1x1_reduce(pool1_3x3_s2)
        conv2_1_1x1_reduce_bn = self.conv2_1_1x1_reduce_bn(conv2_1_1x1_reduce)
        conv2_1_1x1_reduce_bnxx = self.conv2_1_1x1_reduce_relu(conv2_1_1x1_reduce_bn)
        conv2_1_3x3 = self.conv2_1_3x3(conv2_1_1x1_reduce_bnxx)
        conv2_1_3x3_bn = self.conv2_1_3x3_bn(conv2_1_3x3)
        conv2_1_3x3_bnxx = self.conv2_1_3x3_relu(conv2_1_3x3_bn)
        conv2_1_1x1_increase = self.conv2_1_1x1_increase(conv2_1_3x3_bnxx)
        conv2_1_1x1_proj = self.conv2_1_1x1_proj(pool1_3x3_s2)
        conv2_1_1x1_increase_bn = self.conv2_1_1x1_increase_bn(conv2_1_1x1_increase)
        conv2_1_1x1_proj_bn = self.conv2_1_1x1_proj_bn(conv2_1_1x1_proj)
        conv2_1 = torch.add(conv2_1_1x1_proj_bn, 1, conv2_1_1x1_increase_bn)
        conv2_1x = self.conv2_1_relu(conv2_1)
        conv2_2_1x1_reduce = self.conv2_2_1x1_reduce(conv2_1x)
        conv2_2_1x1_reduce_bn = self.conv2_2_1x1_reduce_bn(conv2_2_1x1_reduce)
        conv2_2_1x1_reduce_bnxx = self.conv2_2_1x1_reduce_relu(conv2_2_1x1_reduce_bn)
        conv2_2_3x3 = self.conv2_2_3x3(conv2_2_1x1_reduce_bnxx)
        conv2_2_3x3_bn = self.conv2_2_3x3_bn(conv2_2_3x3)
        conv2_2_3x3_bnxx = self.conv2_2_3x3_relu(conv2_2_3x3_bn)
        conv2_2_1x1_increase = self.conv2_2_1x1_increase(conv2_2_3x3_bnxx)
        conv2_2_1x1_increase_bn = self.conv2_2_1x1_increase_bn(conv2_2_1x1_increase)
        conv2_2 = torch.add(conv2_1x, 1, conv2_2_1x1_increase_bn)
        conv2_2x = self.conv2_2_relu(conv2_2)
        conv2_3_1x1_reduce = self.conv2_3_1x1_reduce(conv2_2x)
        conv2_3_1x1_reduce_bn = self.conv2_3_1x1_reduce_bn(conv2_3_1x1_reduce)
        conv2_3_1x1_reduce_bnxx = self.conv2_3_1x1_reduce_relu(conv2_3_1x1_reduce_bn)
        conv2_3_3x3 = self.conv2_3_3x3(conv2_3_1x1_reduce_bnxx)
        conv2_3_3x3_bn = self.conv2_3_3x3_bn(conv2_3_3x3)
        conv2_3_3x3_bnxx = self.conv2_3_3x3_relu(conv2_3_3x3_bn)
        conv2_3_1x1_increase = self.conv2_3_1x1_increase(conv2_3_3x3_bnxx)
        conv2_3_1x1_increase_bn = self.conv2_3_1x1_increase_bn(conv2_3_1x1_increase)
        conv2_3 = torch.add(conv2_2x, 1, conv2_3_1x1_increase_bn)
        conv2_3x = self.conv2_3_relu(conv2_3)
        conv3_1_1x1_reduce = self.conv3_1_1x1_reduce(conv2_3x)
        conv3_1_1x1_reduce_bn = self.conv3_1_1x1_reduce_bn(conv3_1_1x1_reduce)
        conv3_1_1x1_reduce_bnxx = self.conv3_1_1x1_reduce_relu(conv3_1_1x1_reduce_bn)
        conv3_1_3x3 = self.conv3_1_3x3(conv3_1_1x1_reduce_bnxx)
        conv3_1_3x3_bn = self.conv3_1_3x3_bn(conv3_1_3x3)
        conv3_1_3x3_bnxx = self.conv3_1_3x3_relu(conv3_1_3x3_bn)
        conv3_1_1x1_increase = self.conv3_1_1x1_increase(conv3_1_3x3_bnxx)
        conv3_1_1x1_proj = self.conv3_1_1x1_proj(conv2_3x)
        conv3_1_1x1_increase_bn = self.conv3_1_1x1_increase_bn(conv3_1_1x1_increase)
        conv3_1_1x1_proj_bn = self.conv3_1_1x1_proj_bn(conv3_1_1x1_proj)
        conv3_1 = torch.add(conv3_1_1x1_proj_bn, 1, conv3_1_1x1_increase_bn)
        conv3_1x = self.conv3_1_relu(conv3_1)
        conv3_2_1x1_reduce = self.conv3_2_1x1_reduce(conv3_1x)
        conv3_2_1x1_reduce_bn = self.conv3_2_1x1_reduce_bn(conv3_2_1x1_reduce)
        conv3_2_1x1_reduce_bnxx = self.conv3_2_1x1_reduce_relu(conv3_2_1x1_reduce_bn)
        conv3_2_3x3 = self.conv3_2_3x3(conv3_2_1x1_reduce_bnxx)
        conv3_2_3x3_bn = self.conv3_2_3x3_bn(conv3_2_3x3)
        conv3_2_3x3_bnxx = self.conv3_2_3x3_relu(conv3_2_3x3_bn)
        conv3_2_1x1_increase = self.conv3_2_1x1_increase(conv3_2_3x3_bnxx)
        conv3_2_1x1_increase_bn = self.conv3_2_1x1_increase_bn(conv3_2_1x1_increase)
        conv3_2 = torch.add(conv3_1x, 1, conv3_2_1x1_increase_bn)
        conv3_2x = self.conv3_2_relu(conv3_2)
        conv3_3_1x1_reduce = self.conv3_3_1x1_reduce(conv3_2x)
        conv3_3_1x1_reduce_bn = self.conv3_3_1x1_reduce_bn(conv3_3_1x1_reduce)
        conv3_3_1x1_reduce_bnxx = self.conv3_3_1x1_reduce_relu(conv3_3_1x1_reduce_bn)
        conv3_3_3x3 = self.conv3_3_3x3(conv3_3_1x1_reduce_bnxx)
        conv3_3_3x3_bn = self.conv3_3_3x3_bn(conv3_3_3x3)
        conv3_3_3x3_bnxx = self.conv3_3_3x3_relu(conv3_3_3x3_bn)
        conv3_3_1x1_increase = self.conv3_3_1x1_increase(conv3_3_3x3_bnxx)
        conv3_3_1x1_increase_bn = self.conv3_3_1x1_increase_bn(conv3_3_1x1_increase)
        conv3_3 = torch.add(conv3_2x, 1, conv3_3_1x1_increase_bn)
        conv3_3x = self.conv3_3_relu(conv3_3)
        conv3_4_1x1_reduce = self.conv3_4_1x1_reduce(conv3_3x)
        conv3_4_1x1_reduce_bn = self.conv3_4_1x1_reduce_bn(conv3_4_1x1_reduce)
        conv3_4_1x1_reduce_bnxx = self.conv3_4_1x1_reduce_relu(conv3_4_1x1_reduce_bn)
        conv3_4_3x3 = self.conv3_4_3x3(conv3_4_1x1_reduce_bnxx)
        conv3_4_3x3_bn = self.conv3_4_3x3_bn(conv3_4_3x3)
        conv3_4_3x3_bnxx = self.conv3_4_3x3_relu(conv3_4_3x3_bn)
        conv3_4_1x1_increase = self.conv3_4_1x1_increase(conv3_4_3x3_bnxx)
        conv3_4_1x1_increase_bn = self.conv3_4_1x1_increase_bn(conv3_4_1x1_increase)
        conv3_4 = torch.add(conv3_3x, 1, conv3_4_1x1_increase_bn)
        conv3_4x = self.conv3_4_relu(conv3_4)
        conv4_1_1x1_reduce = self.conv4_1_1x1_reduce(conv3_4x)
        conv4_1_1x1_reduce_bn = self.conv4_1_1x1_reduce_bn(conv4_1_1x1_reduce)
        conv4_1_1x1_reduce_bnxx = self.conv4_1_1x1_reduce_relu(conv4_1_1x1_reduce_bn)
        conv4_1_3x3 = self.conv4_1_3x3(conv4_1_1x1_reduce_bnxx)
        conv4_1_3x3_bn = self.conv4_1_3x3_bn(conv4_1_3x3)
        conv4_1_3x3_bnxx = self.conv4_1_3x3_relu(conv4_1_3x3_bn)
        conv4_1_1x1_increase = self.conv4_1_1x1_increase(conv4_1_3x3_bnxx)
        conv4_1_1x1_proj = self.conv4_1_1x1_proj(conv3_4x)
        conv4_1_1x1_increase_bn = self.conv4_1_1x1_increase_bn(conv4_1_1x1_increase)
        conv4_1_1x1_proj_bn = self.conv4_1_1x1_proj_bn(conv4_1_1x1_proj)
        conv4_1 = torch.add(conv4_1_1x1_proj_bn, 1, conv4_1_1x1_increase_bn)
        conv4_1x = self.conv4_1_relu(conv4_1)
        conv4_2_1x1_reduce = self.conv4_2_1x1_reduce(conv4_1x)
        conv4_2_1x1_reduce_bn = self.conv4_2_1x1_reduce_bn(conv4_2_1x1_reduce)
        conv4_2_1x1_reduce_bnxx = self.conv4_2_1x1_reduce_relu(conv4_2_1x1_reduce_bn)
        conv4_2_3x3 = self.conv4_2_3x3(conv4_2_1x1_reduce_bnxx)
        conv4_2_3x3_bn = self.conv4_2_3x3_bn(conv4_2_3x3)
        conv4_2_3x3_bnxx = self.conv4_2_3x3_relu(conv4_2_3x3_bn)
        conv4_2_1x1_increase = self.conv4_2_1x1_increase(conv4_2_3x3_bnxx)
        conv4_2_1x1_increase_bn = self.conv4_2_1x1_increase_bn(conv4_2_1x1_increase)
        conv4_2 = torch.add(conv4_1x, 1, conv4_2_1x1_increase_bn)
        conv4_2x = self.conv4_2_relu(conv4_2)
        conv4_3_1x1_reduce = self.conv4_3_1x1_reduce(conv4_2x)
        conv4_3_1x1_reduce_bn = self.conv4_3_1x1_reduce_bn(conv4_3_1x1_reduce)
        conv4_3_1x1_reduce_bnxx = self.conv4_3_1x1_reduce_relu(conv4_3_1x1_reduce_bn)
        conv4_3_3x3 = self.conv4_3_3x3(conv4_3_1x1_reduce_bnxx)
        conv4_3_3x3_bn = self.conv4_3_3x3_bn(conv4_3_3x3)
        conv4_3_3x3_bnxx = self.conv4_3_3x3_relu(conv4_3_3x3_bn)
        conv4_3_1x1_increase = self.conv4_3_1x1_increase(conv4_3_3x3_bnxx)
        conv4_3_1x1_increase_bn = self.conv4_3_1x1_increase_bn(conv4_3_1x1_increase)
        conv4_3 = torch.add(conv4_2x, 1, conv4_3_1x1_increase_bn)
        conv4_3x = self.conv4_3_relu(conv4_3)
        conv4_4_1x1_reduce = self.conv4_4_1x1_reduce(conv4_3x)
        conv4_4_1x1_reduce_bn = self.conv4_4_1x1_reduce_bn(conv4_4_1x1_reduce)
        conv4_4_1x1_reduce_bnxx = self.conv4_4_1x1_reduce_relu(conv4_4_1x1_reduce_bn)
        conv4_4_3x3 = self.conv4_4_3x3(conv4_4_1x1_reduce_bnxx)
        conv4_4_3x3_bn = self.conv4_4_3x3_bn(conv4_4_3x3)
        conv4_4_3x3_bnxx = self.conv4_4_3x3_relu(conv4_4_3x3_bn)
        conv4_4_1x1_increase = self.conv4_4_1x1_increase(conv4_4_3x3_bnxx)
        conv4_4_1x1_increase_bn = self.conv4_4_1x1_increase_bn(conv4_4_1x1_increase)
        conv4_4 = torch.add(conv4_3x, 1, conv4_4_1x1_increase_bn)
        conv4_4x = self.conv4_4_relu(conv4_4)
        conv4_5_1x1_reduce = self.conv4_5_1x1_reduce(conv4_4x)
        conv4_5_1x1_reduce_bn = self.conv4_5_1x1_reduce_bn(conv4_5_1x1_reduce)
        conv4_5_1x1_reduce_bnxx = self.conv4_5_1x1_reduce_relu(conv4_5_1x1_reduce_bn)
        conv4_5_3x3 = self.conv4_5_3x3(conv4_5_1x1_reduce_bnxx)
        conv4_5_3x3_bn = self.conv4_5_3x3_bn(conv4_5_3x3)
        conv4_5_3x3_bnxx = self.conv4_5_3x3_relu(conv4_5_3x3_bn)
        conv4_5_1x1_increase = self.conv4_5_1x1_increase(conv4_5_3x3_bnxx)
        conv4_5_1x1_increase_bn = self.conv4_5_1x1_increase_bn(conv4_5_1x1_increase)
        conv4_5 = torch.add(conv4_4x, 1, conv4_5_1x1_increase_bn)
        conv4_5x = self.conv4_5_relu(conv4_5)
        conv4_6_1x1_reduce = self.conv4_6_1x1_reduce(conv4_5x)
        conv4_6_1x1_reduce_bn = self.conv4_6_1x1_reduce_bn(conv4_6_1x1_reduce)
        conv4_6_1x1_reduce_bnxx = self.conv4_6_1x1_reduce_relu(conv4_6_1x1_reduce_bn)
        conv4_6_3x3 = self.conv4_6_3x3(conv4_6_1x1_reduce_bnxx)
        conv4_6_3x3_bn = self.conv4_6_3x3_bn(conv4_6_3x3)
        conv4_6_3x3_bnxx = self.conv4_6_3x3_relu(conv4_6_3x3_bn)
        conv4_6_1x1_increase = self.conv4_6_1x1_increase(conv4_6_3x3_bnxx)
        conv4_6_1x1_increase_bn = self.conv4_6_1x1_increase_bn(conv4_6_1x1_increase)
        conv4_6 = torch.add(conv4_5x, 1, conv4_6_1x1_increase_bn)
        conv4_6x = self.conv4_6_relu(conv4_6)
        conv5_1_1x1_reduce = self.conv5_1_1x1_reduce(conv4_6x)
        conv5_1_1x1_reduce_bn = self.conv5_1_1x1_reduce_bn(conv5_1_1x1_reduce)
        conv5_1_1x1_reduce_bnxx = self.conv5_1_1x1_reduce_relu(conv5_1_1x1_reduce_bn)
        conv5_1_3x3 = self.conv5_1_3x3(conv5_1_1x1_reduce_bnxx)
        conv5_1_3x3_bn = self.conv5_1_3x3_bn(conv5_1_3x3)
        conv5_1_3x3_bnxx = self.conv5_1_3x3_relu(conv5_1_3x3_bn)
        conv5_1_1x1_increase = self.conv5_1_1x1_increase(conv5_1_3x3_bnxx)
        conv5_1_1x1_proj = self.conv5_1_1x1_proj(conv4_6x)
        conv5_1_1x1_increase_bn = self.conv5_1_1x1_increase_bn(conv5_1_1x1_increase)
        conv5_1_1x1_proj_bn = self.conv5_1_1x1_proj_bn(conv5_1_1x1_proj)
        conv5_1 = torch.add(conv5_1_1x1_proj_bn, 1, conv5_1_1x1_increase_bn)
        conv5_1x = self.conv5_1_relu(conv5_1)
        conv5_2_1x1_reduce = self.conv5_2_1x1_reduce(conv5_1x)
        conv5_2_1x1_reduce_bn = self.conv5_2_1x1_reduce_bn(conv5_2_1x1_reduce)
        conv5_2_1x1_reduce_bnxx = self.conv5_2_1x1_reduce_relu(conv5_2_1x1_reduce_bn)
        conv5_2_3x3 = self.conv5_2_3x3(conv5_2_1x1_reduce_bnxx)
        conv5_2_3x3_bn = self.conv5_2_3x3_bn(conv5_2_3x3)
        conv5_2_3x3_bnxx = self.conv5_2_3x3_relu(conv5_2_3x3_bn)
        conv5_2_1x1_increase = self.conv5_2_1x1_increase(conv5_2_3x3_bnxx)
        conv5_2_1x1_increase_bn = self.conv5_2_1x1_increase_bn(conv5_2_1x1_increase)
        conv5_2 = torch.add(conv5_1x, 1, conv5_2_1x1_increase_bn)
        conv5_2x = self.conv5_2_relu(conv5_2)
        conv5_3_1x1_reduce = self.conv5_3_1x1_reduce(conv5_2x)
        conv5_3_1x1_reduce_bn = self.conv5_3_1x1_reduce_bn(conv5_3_1x1_reduce)
        conv5_3_1x1_reduce_bnxx = self.conv5_3_1x1_reduce_relu(conv5_3_1x1_reduce_bn)
        conv5_3_3x3 = self.conv5_3_3x3(conv5_3_1x1_reduce_bnxx)
        conv5_3_3x3_drop = self.conv5_3_3x3_drop(conv5_3_3x3)
        conv5_3_3x3_bn = self.conv5_3_3x3_bn(conv5_3_3x3_drop)
        conv5_3_3x3_bnxx = self.conv5_3_3x3_relu(conv5_3_3x3_bn)
        conv5_3_1x1_increase = self.conv5_3_1x1_increase(conv5_3_3x3_bnxx)
        conv5_3_1x1_increase_drop = self.conv5_3_1x1_increase_drop(conv5_3_1x1_increase)
        conv5_3_1x1_increase_bn = self.conv5_3_1x1_increase_bn(conv5_3_1x1_increase_drop)
        conv5_3 = torch.add(conv5_2x, 1, conv5_3_1x1_increase_bn)
        conv5_3x = self.conv5_3_relu(conv5_3)
        pool5_7x7_s1 = self.pool5_7x7_s1(conv5_3x)
        prediction = self.classifier(pool5_7x7_s1)
        return prediction

    def forward_debug(self, data):
        """This purpose of this function is to provide an easy debugging
        utility for the converted network.  Cloning is used to prevent in-place
        operations from modifying feature artefacts. You can prevent the
        generation of this function by setting `debug_mode = False` in the
        importer tool.
        """
        conv1_7x7_s2 = self.conv1_7x7_s2(data)
        self.debug_feats["conv1_7x7_s2"] = conv1_7x7_s2.clone()
        conv1_7x7_s2_bn = self.conv1_7x7_s2_bn(conv1_7x7_s2)
        self.debug_feats["conv1_7x7_s2_bn"] = conv1_7x7_s2_bn.clone()
        conv1_7x7_s2_bnxx = self.conv1_relu_7x7_s2(conv1_7x7_s2_bn)
        self.debug_feats["conv1_7x7_s2_bnxx"] = conv1_7x7_s2_bnxx.clone()
        pool1_3x3_s2 = self.pool1_3x3_s2(conv1_7x7_s2_bnxx)
        self.debug_feats["pool1_3x3_s2"] = pool1_3x3_s2.clone()
        conv2_1_1x1_reduce = self.conv2_1_1x1_reduce(pool1_3x3_s2)
        self.debug_feats["conv2_1_1x1_reduce"] = conv2_1_1x1_reduce.clone()
        conv2_1_1x1_reduce_bn = self.conv2_1_1x1_reduce_bn(conv2_1_1x1_reduce)
        self.debug_feats["conv2_1_1x1_reduce_bn"] = conv2_1_1x1_reduce_bn.clone()
        conv2_1_1x1_reduce_bnxx = self.conv2_1_1x1_reduce_relu(conv2_1_1x1_reduce_bn)
        self.debug_feats["conv2_1_1x1_reduce_bnxx"] = conv2_1_1x1_reduce_bnxx.clone()
        conv2_1_3x3 = self.conv2_1_3x3(conv2_1_1x1_reduce_bnxx)
        self.debug_feats["conv2_1_3x3"] = conv2_1_3x3.clone()
        conv2_1_3x3_bn = self.conv2_1_3x3_bn(conv2_1_3x3)
        self.debug_feats["conv2_1_3x3_bn"] = conv2_1_3x3_bn.clone()
        conv2_1_3x3_bnxx = self.conv2_1_3x3_relu(conv2_1_3x3_bn)
        self.debug_feats["conv2_1_3x3_bnxx"] = conv2_1_3x3_bnxx.clone()
        conv2_1_1x1_increase = self.conv2_1_1x1_increase(conv2_1_3x3_bnxx)
        self.debug_feats["conv2_1_1x1_increase"] = conv2_1_1x1_increase.clone()
        conv2_1_1x1_proj = self.conv2_1_1x1_proj(pool1_3x3_s2)
        self.debug_feats["conv2_1_1x1_proj"] = conv2_1_1x1_proj.clone()
        conv2_1_1x1_increase_bn = self.conv2_1_1x1_increase_bn(conv2_1_1x1_increase)
        self.debug_feats["conv2_1_1x1_increase_bn"] = conv2_1_1x1_increase_bn.clone()
        conv2_1_1x1_proj_bn = self.conv2_1_1x1_proj_bn(conv2_1_1x1_proj)
        self.debug_feats["conv2_1_1x1_proj_bn"] = conv2_1_1x1_proj_bn.clone()
        conv2_1 = torch.add(conv2_1_1x1_proj_bn, 1, conv2_1_1x1_increase_bn)
        self.debug_feats["conv2_1"] = conv2_1.clone()
        conv2_1x = self.conv2_1_relu(conv2_1)
        self.debug_feats["conv2_1x"] = conv2_1x.clone()
        conv2_2_1x1_reduce = self.conv2_2_1x1_reduce(conv2_1x)
        self.debug_feats["conv2_2_1x1_reduce"] = conv2_2_1x1_reduce.clone()
        conv2_2_1x1_reduce_bn = self.conv2_2_1x1_reduce_bn(conv2_2_1x1_reduce)
        self.debug_feats["conv2_2_1x1_reduce_bn"] = conv2_2_1x1_reduce_bn.clone()
        conv2_2_1x1_reduce_bnxx = self.conv2_2_1x1_reduce_relu(conv2_2_1x1_reduce_bn)
        self.debug_feats["conv2_2_1x1_reduce_bnxx"] = conv2_2_1x1_reduce_bnxx.clone()
        conv2_2_3x3 = self.conv2_2_3x3(conv2_2_1x1_reduce_bnxx)
        self.debug_feats["conv2_2_3x3"] = conv2_2_3x3.clone()
        conv2_2_3x3_bn = self.conv2_2_3x3_bn(conv2_2_3x3)
        self.debug_feats["conv2_2_3x3_bn"] = conv2_2_3x3_bn.clone()
        conv2_2_3x3_bnxx = self.conv2_2_3x3_relu(conv2_2_3x3_bn)
        self.debug_feats["conv2_2_3x3_bnxx"] = conv2_2_3x3_bnxx.clone()
        conv2_2_1x1_increase = self.conv2_2_1x1_increase(conv2_2_3x3_bnxx)
        self.debug_feats["conv2_2_1x1_increase"] = conv2_2_1x1_increase.clone()
        conv2_2_1x1_increase_bn = self.conv2_2_1x1_increase_bn(conv2_2_1x1_increase)
        self.debug_feats["conv2_2_1x1_increase_bn"] = conv2_2_1x1_increase_bn.clone()
        conv2_2 = torch.add(conv2_1x, 1, conv2_2_1x1_increase_bn)
        self.debug_feats["conv2_2"] = conv2_2.clone()
        conv2_2x = self.conv2_2_relu(conv2_2)
        self.debug_feats["conv2_2x"] = conv2_2x.clone()
        conv2_3_1x1_reduce = self.conv2_3_1x1_reduce(conv2_2x)
        self.debug_feats["conv2_3_1x1_reduce"] = conv2_3_1x1_reduce.clone()
        conv2_3_1x1_reduce_bn = self.conv2_3_1x1_reduce_bn(conv2_3_1x1_reduce)
        self.debug_feats["conv2_3_1x1_reduce_bn"] = conv2_3_1x1_reduce_bn.clone()
        conv2_3_1x1_reduce_bnxx = self.conv2_3_1x1_reduce_relu(conv2_3_1x1_reduce_bn)
        self.debug_feats["conv2_3_1x1_reduce_bnxx"] = conv2_3_1x1_reduce_bnxx.clone()
        conv2_3_3x3 = self.conv2_3_3x3(conv2_3_1x1_reduce_bnxx)
        self.debug_feats["conv2_3_3x3"] = conv2_3_3x3.clone()
        conv2_3_3x3_bn = self.conv2_3_3x3_bn(conv2_3_3x3)
        self.debug_feats["conv2_3_3x3_bn"] = conv2_3_3x3_bn.clone()
        conv2_3_3x3_bnxx = self.conv2_3_3x3_relu(conv2_3_3x3_bn)
        self.debug_feats["conv2_3_3x3_bnxx"] = conv2_3_3x3_bnxx.clone()
        conv2_3_1x1_increase = self.conv2_3_1x1_increase(conv2_3_3x3_bnxx)
        self.debug_feats["conv2_3_1x1_increase"] = conv2_3_1x1_increase.clone()
        conv2_3_1x1_increase_bn = self.conv2_3_1x1_increase_bn(conv2_3_1x1_increase)
        self.debug_feats["conv2_3_1x1_increase_bn"] = conv2_3_1x1_increase_bn.clone()
        conv2_3 = torch.add(conv2_2x, 1, conv2_3_1x1_increase_bn)
        self.debug_feats["conv2_3"] = conv2_3.clone()
        conv2_3x = self.conv2_3_relu(conv2_3)
        self.debug_feats["conv2_3x"] = conv2_3x.clone()
        conv3_1_1x1_reduce = self.conv3_1_1x1_reduce(conv2_3x)
        self.debug_feats["conv3_1_1x1_reduce"] = conv3_1_1x1_reduce.clone()
        conv3_1_1x1_reduce_bn = self.conv3_1_1x1_reduce_bn(conv3_1_1x1_reduce)
        self.debug_feats["conv3_1_1x1_reduce_bn"] = conv3_1_1x1_reduce_bn.clone()
        conv3_1_1x1_reduce_bnxx = self.conv3_1_1x1_reduce_relu(conv3_1_1x1_reduce_bn)
        self.debug_feats["conv3_1_1x1_reduce_bnxx"] = conv3_1_1x1_reduce_bnxx.clone()
        conv3_1_3x3 = self.conv3_1_3x3(conv3_1_1x1_reduce_bnxx)
        self.debug_feats["conv3_1_3x3"] = conv3_1_3x3.clone()
        conv3_1_3x3_bn = self.conv3_1_3x3_bn(conv3_1_3x3)
        self.debug_feats["conv3_1_3x3_bn"] = conv3_1_3x3_bn.clone()
        conv3_1_3x3_bnxx = self.conv3_1_3x3_relu(conv3_1_3x3_bn)
        self.debug_feats["conv3_1_3x3_bnxx"] = conv3_1_3x3_bnxx.clone()
        conv3_1_1x1_increase = self.conv3_1_1x1_increase(conv3_1_3x3_bnxx)
        self.debug_feats["conv3_1_1x1_increase"] = conv3_1_1x1_increase.clone()
        conv3_1_1x1_proj = self.conv3_1_1x1_proj(conv2_3x)
        self.debug_feats["conv3_1_1x1_proj"] = conv3_1_1x1_proj.clone()
        conv3_1_1x1_increase_bn = self.conv3_1_1x1_increase_bn(conv3_1_1x1_increase)
        self.debug_feats["conv3_1_1x1_increase_bn"] = conv3_1_1x1_increase_bn.clone()
        conv3_1_1x1_proj_bn = self.conv3_1_1x1_proj_bn(conv3_1_1x1_proj)
        self.debug_feats["conv3_1_1x1_proj_bn"] = conv3_1_1x1_proj_bn.clone()
        conv3_1 = torch.add(conv3_1_1x1_proj_bn, 1, conv3_1_1x1_increase_bn)
        self.debug_feats["conv3_1"] = conv3_1.clone()
        conv3_1x = self.conv3_1_relu(conv3_1)
        self.debug_feats["conv3_1x"] = conv3_1x.clone()
        conv3_2_1x1_reduce = self.conv3_2_1x1_reduce(conv3_1x)
        self.debug_feats["conv3_2_1x1_reduce"] = conv3_2_1x1_reduce.clone()
        conv3_2_1x1_reduce_bn = self.conv3_2_1x1_reduce_bn(conv3_2_1x1_reduce)
        self.debug_feats["conv3_2_1x1_reduce_bn"] = conv3_2_1x1_reduce_bn.clone()
        conv3_2_1x1_reduce_bnxx = self.conv3_2_1x1_reduce_relu(conv3_2_1x1_reduce_bn)
        self.debug_feats["conv3_2_1x1_reduce_bnxx"] = conv3_2_1x1_reduce_bnxx.clone()
        conv3_2_3x3 = self.conv3_2_3x3(conv3_2_1x1_reduce_bnxx)
        self.debug_feats["conv3_2_3x3"] = conv3_2_3x3.clone()
        conv3_2_3x3_bn = self.conv3_2_3x3_bn(conv3_2_3x3)
        self.debug_feats["conv3_2_3x3_bn"] = conv3_2_3x3_bn.clone()
        conv3_2_3x3_bnxx = self.conv3_2_3x3_relu(conv3_2_3x3_bn)
        self.debug_feats["conv3_2_3x3_bnxx"] = conv3_2_3x3_bnxx.clone()
        conv3_2_1x1_increase = self.conv3_2_1x1_increase(conv3_2_3x3_bnxx)
        self.debug_feats["conv3_2_1x1_increase"] = conv3_2_1x1_increase.clone()
        conv3_2_1x1_increase_bn = self.conv3_2_1x1_increase_bn(conv3_2_1x1_increase)
        self.debug_feats["conv3_2_1x1_increase_bn"] = conv3_2_1x1_increase_bn.clone()
        conv3_2 = torch.add(conv3_1x, 1, conv3_2_1x1_increase_bn)
        self.debug_feats["conv3_2"] = conv3_2.clone()
        conv3_2x = self.conv3_2_relu(conv3_2)
        self.debug_feats["conv3_2x"] = conv3_2x.clone()
        conv3_3_1x1_reduce = self.conv3_3_1x1_reduce(conv3_2x)
        self.debug_feats["conv3_3_1x1_reduce"] = conv3_3_1x1_reduce.clone()
        conv3_3_1x1_reduce_bn = self.conv3_3_1x1_reduce_bn(conv3_3_1x1_reduce)
        self.debug_feats["conv3_3_1x1_reduce_bn"] = conv3_3_1x1_reduce_bn.clone()
        conv3_3_1x1_reduce_bnxx = self.conv3_3_1x1_reduce_relu(conv3_3_1x1_reduce_bn)
        self.debug_feats["conv3_3_1x1_reduce_bnxx"] = conv3_3_1x1_reduce_bnxx.clone()
        conv3_3_3x3 = self.conv3_3_3x3(conv3_3_1x1_reduce_bnxx)
        self.debug_feats["conv3_3_3x3"] = conv3_3_3x3.clone()
        conv3_3_3x3_bn = self.conv3_3_3x3_bn(conv3_3_3x3)
        self.debug_feats["conv3_3_3x3_bn"] = conv3_3_3x3_bn.clone()
        conv3_3_3x3_bnxx = self.conv3_3_3x3_relu(conv3_3_3x3_bn)
        self.debug_feats["conv3_3_3x3_bnxx"] = conv3_3_3x3_bnxx.clone()
        conv3_3_1x1_increase = self.conv3_3_1x1_increase(conv3_3_3x3_bnxx)
        self.debug_feats["conv3_3_1x1_increase"] = conv3_3_1x1_increase.clone()
        conv3_3_1x1_increase_bn = self.conv3_3_1x1_increase_bn(conv3_3_1x1_increase)
        self.debug_feats["conv3_3_1x1_increase_bn"] = conv3_3_1x1_increase_bn.clone()
        conv3_3 = torch.add(conv3_2x, 1, conv3_3_1x1_increase_bn)
        self.debug_feats["conv3_3"] = conv3_3.clone()
        conv3_3x = self.conv3_3_relu(conv3_3)
        self.debug_feats["conv3_3x"] = conv3_3x.clone()
        conv3_4_1x1_reduce = self.conv3_4_1x1_reduce(conv3_3x)
        self.debug_feats["conv3_4_1x1_reduce"] = conv3_4_1x1_reduce.clone()
        conv3_4_1x1_reduce_bn = self.conv3_4_1x1_reduce_bn(conv3_4_1x1_reduce)
        self.debug_feats["conv3_4_1x1_reduce_bn"] = conv3_4_1x1_reduce_bn.clone()
        conv3_4_1x1_reduce_bnxx = self.conv3_4_1x1_reduce_relu(conv3_4_1x1_reduce_bn)
        self.debug_feats["conv3_4_1x1_reduce_bnxx"] = conv3_4_1x1_reduce_bnxx.clone()
        conv3_4_3x3 = self.conv3_4_3x3(conv3_4_1x1_reduce_bnxx)
        self.debug_feats["conv3_4_3x3"] = conv3_4_3x3.clone()
        conv3_4_3x3_bn = self.conv3_4_3x3_bn(conv3_4_3x3)
        self.debug_feats["conv3_4_3x3_bn"] = conv3_4_3x3_bn.clone()
        conv3_4_3x3_bnxx = self.conv3_4_3x3_relu(conv3_4_3x3_bn)
        self.debug_feats["conv3_4_3x3_bnxx"] = conv3_4_3x3_bnxx.clone()
        conv3_4_1x1_increase = self.conv3_4_1x1_increase(conv3_4_3x3_bnxx)
        self.debug_feats["conv3_4_1x1_increase"] = conv3_4_1x1_increase.clone()
        conv3_4_1x1_increase_bn = self.conv3_4_1x1_increase_bn(conv3_4_1x1_increase)
        self.debug_feats["conv3_4_1x1_increase_bn"] = conv3_4_1x1_increase_bn.clone()
        conv3_4 = torch.add(conv3_3x, 1, conv3_4_1x1_increase_bn)
        self.debug_feats["conv3_4"] = conv3_4.clone()
        conv3_4x = self.conv3_4_relu(conv3_4)
        self.debug_feats["conv3_4x"] = conv3_4x.clone()
        conv4_1_1x1_reduce = self.conv4_1_1x1_reduce(conv3_4x)
        self.debug_feats["conv4_1_1x1_reduce"] = conv4_1_1x1_reduce.clone()
        conv4_1_1x1_reduce_bn = self.conv4_1_1x1_reduce_bn(conv4_1_1x1_reduce)
        self.debug_feats["conv4_1_1x1_reduce_bn"] = conv4_1_1x1_reduce_bn.clone()
        conv4_1_1x1_reduce_bnxx = self.conv4_1_1x1_reduce_relu(conv4_1_1x1_reduce_bn)
        self.debug_feats["conv4_1_1x1_reduce_bnxx"] = conv4_1_1x1_reduce_bnxx.clone()
        conv4_1_3x3 = self.conv4_1_3x3(conv4_1_1x1_reduce_bnxx)
        self.debug_feats["conv4_1_3x3"] = conv4_1_3x3.clone()
        conv4_1_3x3_bn = self.conv4_1_3x3_bn(conv4_1_3x3)
        self.debug_feats["conv4_1_3x3_bn"] = conv4_1_3x3_bn.clone()
        conv4_1_3x3_bnxx = self.conv4_1_3x3_relu(conv4_1_3x3_bn)
        self.debug_feats["conv4_1_3x3_bnxx"] = conv4_1_3x3_bnxx.clone()
        conv4_1_1x1_increase = self.conv4_1_1x1_increase(conv4_1_3x3_bnxx)
        self.debug_feats["conv4_1_1x1_increase"] = conv4_1_1x1_increase.clone()
        conv4_1_1x1_proj = self.conv4_1_1x1_proj(conv3_4x)
        self.debug_feats["conv4_1_1x1_proj"] = conv4_1_1x1_proj.clone()
        conv4_1_1x1_increase_bn = self.conv4_1_1x1_increase_bn(conv4_1_1x1_increase)
        self.debug_feats["conv4_1_1x1_increase_bn"] = conv4_1_1x1_increase_bn.clone()
        conv4_1_1x1_proj_bn = self.conv4_1_1x1_proj_bn(conv4_1_1x1_proj)
        self.debug_feats["conv4_1_1x1_proj_bn"] = conv4_1_1x1_proj_bn.clone()
        conv4_1 = torch.add(conv4_1_1x1_proj_bn, 1, conv4_1_1x1_increase_bn)
        self.debug_feats["conv4_1"] = conv4_1.clone()
        conv4_1x = self.conv4_1_relu(conv4_1)
        self.debug_feats["conv4_1x"] = conv4_1x.clone()
        conv4_2_1x1_reduce = self.conv4_2_1x1_reduce(conv4_1x)
        self.debug_feats["conv4_2_1x1_reduce"] = conv4_2_1x1_reduce.clone()
        conv4_2_1x1_reduce_bn = self.conv4_2_1x1_reduce_bn(conv4_2_1x1_reduce)
        self.debug_feats["conv4_2_1x1_reduce_bn"] = conv4_2_1x1_reduce_bn.clone()
        conv4_2_1x1_reduce_bnxx = self.conv4_2_1x1_reduce_relu(conv4_2_1x1_reduce_bn)
        self.debug_feats["conv4_2_1x1_reduce_bnxx"] = conv4_2_1x1_reduce_bnxx.clone()
        conv4_2_3x3 = self.conv4_2_3x3(conv4_2_1x1_reduce_bnxx)
        self.debug_feats["conv4_2_3x3"] = conv4_2_3x3.clone()
        conv4_2_3x3_bn = self.conv4_2_3x3_bn(conv4_2_3x3)
        self.debug_feats["conv4_2_3x3_bn"] = conv4_2_3x3_bn.clone()
        conv4_2_3x3_bnxx = self.conv4_2_3x3_relu(conv4_2_3x3_bn)
        self.debug_feats["conv4_2_3x3_bnxx"] = conv4_2_3x3_bnxx.clone()
        conv4_2_1x1_increase = self.conv4_2_1x1_increase(conv4_2_3x3_bnxx)
        self.debug_feats["conv4_2_1x1_increase"] = conv4_2_1x1_increase.clone()
        conv4_2_1x1_increase_bn = self.conv4_2_1x1_increase_bn(conv4_2_1x1_increase)
        self.debug_feats["conv4_2_1x1_increase_bn"] = conv4_2_1x1_increase_bn.clone()
        conv4_2 = torch.add(conv4_1x, 1, conv4_2_1x1_increase_bn)
        self.debug_feats["conv4_2"] = conv4_2.clone()
        conv4_2x = self.conv4_2_relu(conv4_2)
        self.debug_feats["conv4_2x"] = conv4_2x.clone()
        conv4_3_1x1_reduce = self.conv4_3_1x1_reduce(conv4_2x)
        self.debug_feats["conv4_3_1x1_reduce"] = conv4_3_1x1_reduce.clone()
        conv4_3_1x1_reduce_bn = self.conv4_3_1x1_reduce_bn(conv4_3_1x1_reduce)
        self.debug_feats["conv4_3_1x1_reduce_bn"] = conv4_3_1x1_reduce_bn.clone()
        conv4_3_1x1_reduce_bnxx = self.conv4_3_1x1_reduce_relu(conv4_3_1x1_reduce_bn)
        self.debug_feats["conv4_3_1x1_reduce_bnxx"] = conv4_3_1x1_reduce_bnxx.clone()
        conv4_3_3x3 = self.conv4_3_3x3(conv4_3_1x1_reduce_bnxx)
        self.debug_feats["conv4_3_3x3"] = conv4_3_3x3.clone()
        conv4_3_3x3_bn = self.conv4_3_3x3_bn(conv4_3_3x3)
        self.debug_feats["conv4_3_3x3_bn"] = conv4_3_3x3_bn.clone()
        conv4_3_3x3_bnxx = self.conv4_3_3x3_relu(conv4_3_3x3_bn)
        self.debug_feats["conv4_3_3x3_bnxx"] = conv4_3_3x3_bnxx.clone()
        conv4_3_1x1_increase = self.conv4_3_1x1_increase(conv4_3_3x3_bnxx)
        self.debug_feats["conv4_3_1x1_increase"] = conv4_3_1x1_increase.clone()
        conv4_3_1x1_increase_bn = self.conv4_3_1x1_increase_bn(conv4_3_1x1_increase)
        self.debug_feats["conv4_3_1x1_increase_bn"] = conv4_3_1x1_increase_bn.clone()
        conv4_3 = torch.add(conv4_2x, 1, conv4_3_1x1_increase_bn)
        self.debug_feats["conv4_3"] = conv4_3.clone()
        conv4_3x = self.conv4_3_relu(conv4_3)
        self.debug_feats["conv4_3x"] = conv4_3x.clone()
        conv4_4_1x1_reduce = self.conv4_4_1x1_reduce(conv4_3x)
        self.debug_feats["conv4_4_1x1_reduce"] = conv4_4_1x1_reduce.clone()
        conv4_4_1x1_reduce_bn = self.conv4_4_1x1_reduce_bn(conv4_4_1x1_reduce)
        self.debug_feats["conv4_4_1x1_reduce_bn"] = conv4_4_1x1_reduce_bn.clone()
        conv4_4_1x1_reduce_bnxx = self.conv4_4_1x1_reduce_relu(conv4_4_1x1_reduce_bn)
        self.debug_feats["conv4_4_1x1_reduce_bnxx"] = conv4_4_1x1_reduce_bnxx.clone()
        conv4_4_3x3 = self.conv4_4_3x3(conv4_4_1x1_reduce_bnxx)
        self.debug_feats["conv4_4_3x3"] = conv4_4_3x3.clone()
        conv4_4_3x3_bn = self.conv4_4_3x3_bn(conv4_4_3x3)
        self.debug_feats["conv4_4_3x3_bn"] = conv4_4_3x3_bn.clone()
        conv4_4_3x3_bnxx = self.conv4_4_3x3_relu(conv4_4_3x3_bn)
        self.debug_feats["conv4_4_3x3_bnxx"] = conv4_4_3x3_bnxx.clone()
        conv4_4_1x1_increase = self.conv4_4_1x1_increase(conv4_4_3x3_bnxx)
        self.debug_feats["conv4_4_1x1_increase"] = conv4_4_1x1_increase.clone()
        conv4_4_1x1_increase_bn = self.conv4_4_1x1_increase_bn(conv4_4_1x1_increase)
        self.debug_feats["conv4_4_1x1_increase_bn"] = conv4_4_1x1_increase_bn.clone()
        conv4_4 = torch.add(conv4_3x, 1, conv4_4_1x1_increase_bn)
        self.debug_feats["conv4_4"] = conv4_4.clone()
        conv4_4x = self.conv4_4_relu(conv4_4)
        self.debug_feats["conv4_4x"] = conv4_4x.clone()
        conv4_5_1x1_reduce = self.conv4_5_1x1_reduce(conv4_4x)
        self.debug_feats["conv4_5_1x1_reduce"] = conv4_5_1x1_reduce.clone()
        conv4_5_1x1_reduce_bn = self.conv4_5_1x1_reduce_bn(conv4_5_1x1_reduce)
        self.debug_feats["conv4_5_1x1_reduce_bn"] = conv4_5_1x1_reduce_bn.clone()
        conv4_5_1x1_reduce_bnxx = self.conv4_5_1x1_reduce_relu(conv4_5_1x1_reduce_bn)
        self.debug_feats["conv4_5_1x1_reduce_bnxx"] = conv4_5_1x1_reduce_bnxx.clone()
        conv4_5_3x3 = self.conv4_5_3x3(conv4_5_1x1_reduce_bnxx)
        self.debug_feats["conv4_5_3x3"] = conv4_5_3x3.clone()
        conv4_5_3x3_bn = self.conv4_5_3x3_bn(conv4_5_3x3)
        self.debug_feats["conv4_5_3x3_bn"] = conv4_5_3x3_bn.clone()
        conv4_5_3x3_bnxx = self.conv4_5_3x3_relu(conv4_5_3x3_bn)
        self.debug_feats["conv4_5_3x3_bnxx"] = conv4_5_3x3_bnxx.clone()
        conv4_5_1x1_increase = self.conv4_5_1x1_increase(conv4_5_3x3_bnxx)
        self.debug_feats["conv4_5_1x1_increase"] = conv4_5_1x1_increase.clone()
        conv4_5_1x1_increase_bn = self.conv4_5_1x1_increase_bn(conv4_5_1x1_increase)
        self.debug_feats["conv4_5_1x1_increase_bn"] = conv4_5_1x1_increase_bn.clone()
        conv4_5 = torch.add(conv4_4x, 1, conv4_5_1x1_increase_bn)
        self.debug_feats["conv4_5"] = conv4_5.clone()
        conv4_5x = self.conv4_5_relu(conv4_5)
        self.debug_feats["conv4_5x"] = conv4_5x.clone()
        conv4_6_1x1_reduce = self.conv4_6_1x1_reduce(conv4_5x)
        self.debug_feats["conv4_6_1x1_reduce"] = conv4_6_1x1_reduce.clone()
        conv4_6_1x1_reduce_bn = self.conv4_6_1x1_reduce_bn(conv4_6_1x1_reduce)
        self.debug_feats["conv4_6_1x1_reduce_bn"] = conv4_6_1x1_reduce_bn.clone()
        conv4_6_1x1_reduce_bnxx = self.conv4_6_1x1_reduce_relu(conv4_6_1x1_reduce_bn)
        self.debug_feats["conv4_6_1x1_reduce_bnxx"] = conv4_6_1x1_reduce_bnxx.clone()
        conv4_6_3x3 = self.conv4_6_3x3(conv4_6_1x1_reduce_bnxx)
        self.debug_feats["conv4_6_3x3"] = conv4_6_3x3.clone()
        conv4_6_3x3_bn = self.conv4_6_3x3_bn(conv4_6_3x3)
        self.debug_feats["conv4_6_3x3_bn"] = conv4_6_3x3_bn.clone()
        conv4_6_3x3_bnxx = self.conv4_6_3x3_relu(conv4_6_3x3_bn)
        self.debug_feats["conv4_6_3x3_bnxx"] = conv4_6_3x3_bnxx.clone()
        conv4_6_1x1_increase = self.conv4_6_1x1_increase(conv4_6_3x3_bnxx)
        self.debug_feats["conv4_6_1x1_increase"] = conv4_6_1x1_increase.clone()
        conv4_6_1x1_increase_bn = self.conv4_6_1x1_increase_bn(conv4_6_1x1_increase)
        self.debug_feats["conv4_6_1x1_increase_bn"] = conv4_6_1x1_increase_bn.clone()
        conv4_6 = torch.add(conv4_5x, 1, conv4_6_1x1_increase_bn)
        self.debug_feats["conv4_6"] = conv4_6.clone()
        conv4_6x = self.conv4_6_relu(conv4_6)
        self.debug_feats["conv4_6x"] = conv4_6x.clone()
        conv5_1_1x1_reduce = self.conv5_1_1x1_reduce(conv4_6x)
        self.debug_feats["conv5_1_1x1_reduce"] = conv5_1_1x1_reduce.clone()
        conv5_1_1x1_reduce_bn = self.conv5_1_1x1_reduce_bn(conv5_1_1x1_reduce)
        self.debug_feats["conv5_1_1x1_reduce_bn"] = conv5_1_1x1_reduce_bn.clone()
        conv5_1_1x1_reduce_bnxx = self.conv5_1_1x1_reduce_relu(conv5_1_1x1_reduce_bn)
        self.debug_feats["conv5_1_1x1_reduce_bnxx"] = conv5_1_1x1_reduce_bnxx.clone()
        conv5_1_3x3 = self.conv5_1_3x3(conv5_1_1x1_reduce_bnxx)
        self.debug_feats["conv5_1_3x3"] = conv5_1_3x3.clone()
        conv5_1_3x3_bn = self.conv5_1_3x3_bn(conv5_1_3x3)
        self.debug_feats["conv5_1_3x3_bn"] = conv5_1_3x3_bn.clone()
        conv5_1_3x3_bnxx = self.conv5_1_3x3_relu(conv5_1_3x3_bn)
        self.debug_feats["conv5_1_3x3_bnxx"] = conv5_1_3x3_bnxx.clone()
        conv5_1_1x1_increase = self.conv5_1_1x1_increase(conv5_1_3x3_bnxx)
        self.debug_feats["conv5_1_1x1_increase"] = conv5_1_1x1_increase.clone()
        conv5_1_1x1_proj = self.conv5_1_1x1_proj(conv4_6x)
        self.debug_feats["conv5_1_1x1_proj"] = conv5_1_1x1_proj.clone()
        conv5_1_1x1_increase_bn = self.conv5_1_1x1_increase_bn(conv5_1_1x1_increase)
        self.debug_feats["conv5_1_1x1_increase_bn"] = conv5_1_1x1_increase_bn.clone()
        conv5_1_1x1_proj_bn = self.conv5_1_1x1_proj_bn(conv5_1_1x1_proj)
        self.debug_feats["conv5_1_1x1_proj_bn"] = conv5_1_1x1_proj_bn.clone()
        conv5_1 = torch.add(conv5_1_1x1_proj_bn, 1, conv5_1_1x1_increase_bn)
        self.debug_feats["conv5_1"] = conv5_1.clone()
        conv5_1x = self.conv5_1_relu(conv5_1)
        self.debug_feats["conv5_1x"] = conv5_1x.clone()
        conv5_2_1x1_reduce = self.conv5_2_1x1_reduce(conv5_1x)
        self.debug_feats["conv5_2_1x1_reduce"] = conv5_2_1x1_reduce.clone()
        conv5_2_1x1_reduce_bn = self.conv5_2_1x1_reduce_bn(conv5_2_1x1_reduce)
        self.debug_feats["conv5_2_1x1_reduce_bn"] = conv5_2_1x1_reduce_bn.clone()
        conv5_2_1x1_reduce_bnxx = self.conv5_2_1x1_reduce_relu(conv5_2_1x1_reduce_bn)
        self.debug_feats["conv5_2_1x1_reduce_bnxx"] = conv5_2_1x1_reduce_bnxx.clone()
        conv5_2_3x3 = self.conv5_2_3x3(conv5_2_1x1_reduce_bnxx)
        self.debug_feats["conv5_2_3x3"] = conv5_2_3x3.clone()
        conv5_2_3x3_bn = self.conv5_2_3x3_bn(conv5_2_3x3)
        self.debug_feats["conv5_2_3x3_bn"] = conv5_2_3x3_bn.clone()
        conv5_2_3x3_bnxx = self.conv5_2_3x3_relu(conv5_2_3x3_bn)
        self.debug_feats["conv5_2_3x3_bnxx"] = conv5_2_3x3_bnxx.clone()
        conv5_2_1x1_increase = self.conv5_2_1x1_increase(conv5_2_3x3_bnxx)
        self.debug_feats["conv5_2_1x1_increase"] = conv5_2_1x1_increase.clone()
        conv5_2_1x1_increase_bn = self.conv5_2_1x1_increase_bn(conv5_2_1x1_increase)
        self.debug_feats["conv5_2_1x1_increase_bn"] = conv5_2_1x1_increase_bn.clone()
        conv5_2 = torch.add(conv5_1x, 1, conv5_2_1x1_increase_bn)
        self.debug_feats["conv5_2"] = conv5_2.clone()
        conv5_2x = self.conv5_2_relu(conv5_2)
        self.debug_feats["conv5_2x"] = conv5_2x.clone()
        conv5_3_1x1_reduce = self.conv5_3_1x1_reduce(conv5_2x)
        self.debug_feats["conv5_3_1x1_reduce"] = conv5_3_1x1_reduce.clone()
        conv5_3_1x1_reduce_bn = self.conv5_3_1x1_reduce_bn(conv5_3_1x1_reduce)
        self.debug_feats["conv5_3_1x1_reduce_bn"] = conv5_3_1x1_reduce_bn.clone()
        conv5_3_1x1_reduce_bnxx = self.conv5_3_1x1_reduce_relu(conv5_3_1x1_reduce_bn)
        self.debug_feats["conv5_3_1x1_reduce_bnxx"] = conv5_3_1x1_reduce_bnxx.clone()
        conv5_3_3x3 = self.conv5_3_3x3(conv5_3_1x1_reduce_bnxx)
        self.debug_feats["conv5_3_3x3"] = conv5_3_3x3.clone()
        conv5_3_3x3_drop = self.conv5_3_3x3_drop(conv5_3_3x3)
        self.debug_feats["conv5_3_3x3_drop"] = conv5_3_3x3_drop.clone()
        conv5_3_3x3_bn = self.conv5_3_3x3_bn(conv5_3_3x3_drop)
        self.debug_feats["conv5_3_3x3_bn"] = conv5_3_3x3_bn.clone()
        conv5_3_3x3_bnxx = self.conv5_3_3x3_relu(conv5_3_3x3_bn)
        self.debug_feats["conv5_3_3x3_bnxx"] = conv5_3_3x3_bnxx.clone()
        conv5_3_1x1_increase = self.conv5_3_1x1_increase(conv5_3_3x3_bnxx)
        self.debug_feats["conv5_3_1x1_increase"] = conv5_3_1x1_increase.clone()
        conv5_3_1x1_increase_drop = self.conv5_3_1x1_increase_drop(conv5_3_1x1_increase)
        self.debug_feats["conv5_3_1x1_increase_drop"] = conv5_3_1x1_increase_drop.clone()
        conv5_3_1x1_increase_bn = self.conv5_3_1x1_increase_bn(conv5_3_1x1_increase_drop)
        self.debug_feats["conv5_3_1x1_increase_bn"] = conv5_3_1x1_increase_bn.clone()
        conv5_3 = torch.add(conv5_2x, 1, conv5_3_1x1_increase_bn)
        self.debug_feats["conv5_3"] = conv5_3.clone()
        conv5_3x = self.conv5_3_relu(conv5_3)
        self.debug_feats["conv5_3x"] = conv5_3x.clone()
        pool5_7x7_s1 = self.pool5_7x7_s1(conv5_3x)
        self.debug_feats["pool5_7x7_s1"] = pool5_7x7_s1.clone()
        prediction = self.classifier(pool5_7x7_s1)
        self.debug_feats["prediction"] = prediction.clone()


def resnet50_ferplus_dag(weights_path=None, **kwargs):
    """
    load imported model instance

    Args:
        weights_path (str): If set, loads model weights from the given path
    """
    model = Resnet50_ferplus_dag()
    if weights_path:
        state_dict = torch.load(weights_path)
        model.load_state_dict(state_dict)
    return model

name: vllm2
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.9.24=h06a4308_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.15=h5eee18b_0
  - pip=24.2=py310h06a4308_0
  - python=3.10.15=he870216_1
  - readline=8.2=h5eee18b_0
  - setuptools=75.1.0=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - wheel=0.44.0=py310h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - absl-py==2.2.2
      - accelerate==1.0.0
      - aiofiles==23.2.1
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.10.9
      - aiosignal==1.3.1
      - aistudio-sdk==0.2.6
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.6.0
      - asciitree==0.3.3
      - astor==0.8.1
      - astropy==6.1.4
      - astropy-iers-data==0.2024.**********.55
      - asttokens==2.4.1
      - async-timeout==4.0.3
      - attrs==24.2.0
      - audioread==3.0.1
      - av==13.1.0
      - babel==2.17.0
      - bce-python-sdk==0.9.29
      - beautifulsoup4==4.12.3
      - blinker==1.9.0
      - bokeh==3.7.2
      - boltons==25.0.0
      - bottleneck==1.4.2
      - braceexpand==0.1.7
      - certifi==2024.8.30
      - cffi==1.17.1
      - charset-normalizer==3.4.0
      - click==8.1.7
      - cloudpickle==3.0.0
      - colorama==0.4.6
      - coloredlogs==15.0.1
      - colorlog==6.9.0
      - contourpy==1.3.0
      - cycler==0.12.1
      - datasets==3.0.1
      - debugpy==1.8.12
      - decode==2024.9.2
      - decorator==5.1.1
      - decord==0.6.0
      - dems==2024.7.2
      - dill==0.3.4
      - diskcache==5.6.3
      - distance==0.1.3
      - distro==1.9.0
      - editdistance==0.8.1
      - einops==0.8.0
      - et-xmlfile==1.1.0
      - exceptiongroup==1.2.2
      - executing==2.1.0
      - fairscale==0.4.13
      - fastapi==0.115.6
      - fasteners==0.19
      - ffmpy==0.5.0
      - filelock==3.13.1
      - fire==0.5.0
      - flash-attn==2.7.2.post1
      - flask==3.1.0
      - flask-babel==4.0.0
      - flatbuffers==25.2.10
      - fonttools==4.54.1
      - frozenlist==1.4.1
      - fsspec==2024.2.0
      - ftfy==6.3.0
      - funcsigs==1.0.2
      - future==1.0.0
      - fvcore==0.1.5.post20221221
      - g2p-en==2.1.0
      - g2pm==0.1.2.5
      - gdown==5.2.0
      - gguf==0.10.0
      - gradio==5.9.1
      - gradio-client==1.5.2
      - h11==0.14.0
      - h5py==3.13.0
      - hf-transfer==0.1.9
      - httpcore==1.0.6
      - httptools==0.6.1
      - httpx==0.27.2
      - huggingface-hub==0.29.1
      - humanfriendly==10.0
      - hyperpyyaml==1.2.2
      - idna==3.10
      - imageio==2.36.1
      - importlib-metadata==8.5.0
      - inflect==7.0.0
      - interegular==0.3.3
      - intervaltree==3.1.0
      - iopath==0.1.10
      - ipdb==0.13.13
      - ipdf==0.0.1
      - ipython==8.29.0
      - itsdangerous==2.2.0
      - jedi==0.19.1
      - jieba==0.42.1
      - jinja2==3.1.3
      - jiter==0.6.1
      - joblib==1.4.2
      - jsonlines==4.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - kaldiio==2.18.1
      - kiwisolver==1.4.7
      - lark==1.2.2
      - lazy-loader==0.4
      - librosa==0.8.1
      - lightning==2.5.0.post0
      - lightning-utilities==0.11.9
      - ligo-segments==1.4.0
      - llvmlite==0.43.0
      - lm-format-enforcer==0.10.6
      - loguru==0.7.3
      - lxml==5.3.2
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matplotlib==3.9.2
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mido==1.3.3
      - mistral-common==1.4.4
      - mock==5.2.0
      - more-itertools==10.5.0
      - mpmath==1.3.0
      - msgpack==1.1.0
      - msgspec==0.18.6
      - multidict==6.1.0
      - multiprocess==*********
      - nara-wpe==0.0.11
      - narwhals==1.33.0
      - nest-asyncio==1.6.0
      - networkx==3.2.1
      - nltk==3.9.1
      - note-seq==0.0.5
      - numba==0.60.0
      - numcodecs==0.13.1
      - numpy==1.23.5
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-ml-py==12.560.30
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.1.105
      - nvidia-nvtx-cu12==12.1.105
      - omegaconf==2.3.0
      - onnx==1.17.0
      - onnxruntime==1.21.0
      - open-clip-torch==2.29.0
      - openai==1.51.2
      - openai-whisper==20240930
      - opencc==1.1.6
      - opencc-python-reimplemented==0.1.7
      - opencv-python==********
      - openpyxl==3.1.5
      - opt-einsum==3.3.0
      - orjson==3.10.13
      - outlines==0.0.46
      - packaging==24.1
      - paddle-bfloat==0.1.7
      - paddle2onnx==2.0.0
      - paddleaudio==1.1.0
      - paddlefsl==1.1.0
      - paddlenlp==2.8.1
      - paddlepaddle==3.0.0
      - paddlepaddle-gpu==2.5.1
      - paddlesde==0.2.5
      - paddleslim==2.6.0
      - paddlespeech-feat==0.1.0
      - pandas==2.2.3
      - parameterized==0.9.0
      - parso==0.8.4
      - partial-json-parser==*******.post4
      - pathos==0.2.8
      - pattern-singleton==1.2.0
      - peft==0.13.2
      - pexpect==4.9.0
      - pillow==10.4.0
      - platformdirs==4.3.6
      - pooch==1.8.2
      - portalocker==2.10.1
      - pox==0.3.5
      - ppdiffusers==0.29.0
      - ppft==*******
      - praatio==5.1.1
      - pretty-midi==0.2.10
      - prettytable==3.16.0
      - prometheus-client==0.21.0
      - prometheus-fastapi-instrumentator==7.0.0
      - prompt-toolkit==3.0.48
      - propcache==0.2.0
      - protobuf==5.28.2
      - psutil==6.0.0
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - py-cpuinfo==9.0.0
      - pyairports==2.1.1
      - pyarrow==17.0.0
      - pybind11==2.13.6
      - pycountry==24.6.1
      - pycparser==2.22
      - pycryptodome==3.22.0
      - pydantic==2.9.2
      - pydantic-core==2.23.4
      - pydub==0.25.1
      - pyerfa==*******
      - pygments==2.18.0
      - pyparsing==3.1.4
      - pypinyin==0.44.0
      - pypinyin-dict==0.9.0
      - pysocks==1.7.1
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-multipart==0.0.20
      - python-speech-features==0.6
      - pytorch-lightning==2.5.0.post0
      - pytorchvideo==0.1.5
      - pytz==2024.2
      - pyworld==0.3.5
      - pyyaml==6.0.2
      - pyzmq==26.2.0
      - rarfile==4.2
      - ray==2.37.0
      - referencing==0.35.1
      - regex==2024.9.11
      - requests==2.32.3
      - requests-mock==1.12.1
      - resampy==0.4.3
      - rich==13.9.4
      - rpds-py==0.20.0
      - ruamel-yaml==0.18.10
      - ruamel-yaml-clib==0.2.12
      - ruff==0.8.5
      - sacrebleu==2.5.1
      - safehttpx==0.1.6
      - safetensors==0.4.5
      - scenedetect==*******
      - scikit-learn==1.5.2
      - scipy==1.14.1
      - semantic-version==2.10.0
      - sentencepiece==0.2.0
      - seqeval==1.2.2
      - shellingham==1.5.4
      - six==1.16.0
      - sniffio==1.3.1
      - socksio==1.0.0
      - sortedcontainers==2.4.0
      - soundfile==0.12.1
      - soupsieve==2.6
      - soxr==0.5.0.post1
      - stack-data==0.6.3
      - starlette==0.41.3
      - swig==4.3.0
      - sympy==1.12
      - tabulate==0.9.0
      - termcolor==2.5.0
      - textgrid==1.6.1
      - threadpoolctl==3.5.0
      - tiktoken==0.7.0
      - timer==0.3.0
      - timm==1.0.15
      - tojyutping==3.2.0
      - tokenizers==0.21.0
      - tomli==2.2.1
      - tomlkit==0.13.2
      - tool-helpers==0.1.2
      - torch==2.4.0+cu121
      - torchaudio==2.4.0+cu121
      - torchmetrics==1.6.1
      - torchvision==0.19.0+cu121
      - tornado==6.4.2
      - tqdm==4.66.5
      - traitlets==5.14.3
      - trampoline==0.1.2
      - transformers==4.49.0
      - transformers-stream-generator==0.0.5
      - triton==3.0.0
      - trl==0.15.2
      - typeguard==2.13.3
      - typer==0.15.1
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - urllib3==1.26.20
      - uvicorn==0.31.0
      - uvloop==0.20.0
      - visualdl==2.5.3
      - vllm==0.6.2
      - watchfiles==0.24.0
      - wcwidth==0.2.13
      - webdataset==0.2.100
      - webrtcvad==2.0.10
      - websockets==13.1
      - werkzeug==3.1.3
      - wget==3.2
      - whisper==1.1.10
      - xarray==2024.2.0
      - xarray-dataclasses==1.8.0
      - xformers==0.0.27.post2
      - xxhash==3.5.0
      - xyzservices==2025.1.0
      - yacs==0.1.8
      - yarl==1.14.0
      - zarr==2.18.3
      - zhon==2.1.1
      - zipp==3.20.2
prefix: /data3/renyong/miniconda3/envs/vllm2

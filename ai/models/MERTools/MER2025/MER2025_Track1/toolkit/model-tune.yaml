tfn:
  hidden_dim: [64, 128]
  dropout: [0.2, 0.3, 0.4, 0.5]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

lmf:
  hidden_dim: [32, 64, 128, 256]
  dropout: [0.2, 0.3, 0.4, 0.5]
  rank: [3, 4, 5, 6]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

lf_dnn:
  hidden_dim: [64, 128, 256]
  dropout: [0.2, 0.3, 0.4, 0.5]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]


mmim:
  hidden_dim: [64, 128, 256]
  dropout: [0.0, 0.1, 0.2, 0.3]
  cpc_layers: [1, 2, 3, 4]
  alpha: [0.0, 0.1, 0.2]
  beta:  [0.0, 0.1, 0.2]
  grad_clip: [0.6, 0.8, 1.0]
  lr: [1e-3, 1e-4]

misa:
  dropout: [0.2, 0.3, 0.4, 0.5]
  hidden_dim: [64, 128, 256]
  sim_weight: [0.0, 0.1, 0.2]
  diff_weight: [0.0, 0.1, 0.2]
  recon_weight: [0.0, 0.1, 0.2]
  grad_clip: [-1.0, 0.8, 1.0]
  lr: [1e-3, 1e-4]

mfn:
  hidden_dim: [128, 256]
  mem_dim: [128]
  dropout: [0.0, 0.3, 0.5, 0.7]
  window_dim: [2]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

graph_mfn:
  hidden_dim: [128, 256]
  mem_dim: [128]
  dropout: [0.0, 0.3, 0.5, 0.7]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

mfm:
  hidden_dim: [128, 256]
  mem_dim: [128]
  dropout: [0.0, 0.3, 0.5, 0.7]
  window_dim: [2]
  lda_xl:  [0.01, 0.1, 0.5, 1.0]
  lda_xa:  [0.01, 0.1, 0.5, 1.0]
  lda_xv:  [0.01, 0.1, 0.5, 1.0]
  lda_mmd: [10, 50, 100]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

ef_lstm:
  dropout: [0.2, 0.3, 0.4, 0.5]
  num_layers: [2, 3, 4]
  hidden_dim: [128, 256, 512, 1024]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

mult:
  layers: [2, 4, 6]
  dropout: [0.0, 0.1, 0.2, 0.3]
  num_heads: [8]
  hidden_dim: [64, 128, 256]
  conv1d_kernel_size: [1, 3]
  grad_clip: [0.6, 0.8, 1.0]
  lr: [1e-3, 1e-4]

mctn:
  hidden_dim: [64, 128, 256]
  dropout: [0.0, 0.1, 0.2, 0.3]
  teacher_forcing_ratio: [0.3, 0.5]
  loss_weight: [0.1, 0.3, 0.5, 0.8, 1.0]
  grad_clip: [0.6, 0.8, 1.0]
  lr: [1e-3, 1e-4]

attention:
  hidden_dim: [64, 128, 256]
  dropout: [0.2, 0.3, 0.4, 0.5]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]

e2e_model:
  hidden_dim: [128, 256] 
  dropout: [0.2, 0.3, 0.4, 0.5]
  grad_clip: [-1.0]
  # lr: [1e-4, 4e-5, 2e-5, 1e-5, 8e-6]
  lr: [1e-3, 1e-4, 1e-5, 1e-6]

videomae_pretrain:
  mae_mask_ratio: [0.90, 0.91, 0.92]
  grad_clip: [-1.0]
  lr: [4e-5, 2e-5, 1e-5, 8e-6]

attention_topn:
  hidden_dim: [64, 128, 256]
  dropout: [0.2, 0.3, 0.4, 0.5]
  grad_clip: [-1.0]
  lr: [1e-3, 1e-4]
import torch
import torch.nn as nn
from .eva_vit import create_eva_vit_g, _cfg
from .processor import ImageTrainProcessor, ImageEvalProcessor


class EVAVisionTower(nn.Module):
    def __init__(self, vision_tower, args, delay_load=False):
        super().__init__()

        self.is_loaded = False

        self.vision_tower_name = vision_tower
        self.select_layer = args.mm_vision_select_layer
        self.select_feature = getattr(args, "mm_vision_select_feature", "patch")

        if not delay_load:
            self.load_model()
        else:
            self.cfg_only = _cfg()

    def load_model(self):
        self.image_processor = ImageTrainProcessor()
        self.image_eval_processor = ImageEvalProcessor()
        self.vision_tower = create_eva_vit_g(
            img_size=224, drop_path_rate=0, use_checkpoint=False, precision="fp16"
        )
        # self.vision_tower.requires_grad_(False)

        self.is_loaded = True

    def feature_select(self, image_forward_outs, select_feature="patch"):
        image_features = image_forward_outs[self.select_layer]
        if select_feature == "patch":
            image_features = image_features[:, 1:]
        elif select_feature == "cls_patch":
            image_features = image_features
        else:
            raise ValueError(f"Unexpected select feature: {self.select_feature}")
        return image_features

    @torch.no_grad()
    def forward(self, images, select_feature="patch"):
        if type(images) is list:
            image_features = []
            for image in images:
                image_forward_out = self.vision_tower.get_intermediate_layers(
                    image.to(device=self.device, dtype=self.dtype).unsqueeze(0),
                )
                image_feature = self.feature_select(image_forward_out, select_feature).to(
                    image.dtype
                )
                image_features.append(image_feature)
        else:
            image_forward_outs = self.vision_tower.get_intermediate_layers(
                images.to(device=self.device, dtype=self.dtype)
            )
            image_features = self.feature_select(image_forward_outs, select_feature).to(
                images.dtype
            )

        return image_features

    @property
    def dummy_feature(self):
        return torch.zeros(1, self.hidden_size, device=self.device, dtype=self.dtype)

    @property
    def dtype(self):
        return self.vision_tower.cls_token.dtype

    @property
    def device(self):
        return self.vision_tower.cls_token.device

    @property
    def config(self):
        if self.is_loaded:
            return self.vision_tower.config
        else:
            return self.cfg_only

    @property
    def hidden_size(self):
        return self.vision_tower.num_features

    @property
    def num_patches(self):
        return (self.config.image_size // self.config.patch_size) ** 2

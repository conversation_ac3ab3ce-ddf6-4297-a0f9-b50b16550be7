Metadata-Version: 2.4
Name: pixel
Version: 0.1.0
Summary: Pixelated Empathy - AI-powered bias detection and mental health platform
Author-email: Pixelated Empathy Team <<EMAIL>>
Requires-Python: >=3.11
Requires-Dist: flask
Requires-Dist: flask-cors
Requires-Dist: pandas
Requires-Dist: numpy
Requires-Dist: scikit-learn
Requires-Dist: pyjwt
Requires-Dist: werkzeug
Requires-Dist: fairlearn
Requires-Dist: shap>=0.41.0
Requires-Dist: lime
Requires-Dist: transformers
Requires-Dist: spacy
Requires-Dist: nltk
Requires-Dist: evaluate
Requires-Dist: cryptography
Requires-Dist: requests
Requires-Dist: python-dotenv
Requires-Dist: numba>=0.56.0
Requires-Dist: faiss-cpu>=1.11.0
Requires-Dist: sentence-transformers>=5.0.0
Requires-Dist: pytest>=8.4.1
Requires-Dist: pytest-asyncio>=1.0.0
Requires-Dist: flake8>=7.3.0
Requires-Dist: matplotlib>=3.10.3
Requires-Dist: mypy>=1.16.1
Requires-Dist: psutil>=7.0.0
Requires-Dist: chardet>=5.2.0
Requires-Dist: pydub>=0.25.1
Requires-Dist: hatch>=1.14.1
Requires-Dist: sourcery>=1.37.0
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Requires-Dist: pytest-mock; extra == "test"
Requires-Dist: pytest-asyncio; extra == "test"
Requires-Dist: coverage; extra == "test"
Provides-Extra: dev
Requires-Dist: black; extra == "dev"
Requires-Dist: ruff; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pyright; extra == "dev"
